/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */
import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { StorageService } from "./storage.service";
import { StorageController } from "./storage.controller";
import { StorageMetadataSchema } from "./entities/storage_metadata.entity";
import { StorageQuotaSchema } from "./entities/storage_quota.entity";
import { FileReminderSchema } from "./entities/file_reminder.entity";
import { AuthModule } from "../auth/auth.module";

@Module({
    controllers: [StorageController],
    providers: [StorageService],
    exports: [StorageService],
    imports: [
        MongooseModule.forFeature([
            {
                name: "storage_metadata",
                schema: StorageMetadataSchema
            },
            {
                name: "storage_quota",
                schema: StorageQuotaSchema
            },
            {
                name: "file_reminder",
                schema: FileReminderSchema
            }
        ]),
        AuthModule,
    ]
})
export class StorageModule {}
