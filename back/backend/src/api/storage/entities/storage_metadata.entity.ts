/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */
import { Schema, Document } from "mongoose";
import aggregatePaginate from "mongoose-aggregate-paginate-v2";

export interface IStorageMetadata extends Document {
    userId: string;
    fileId: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    fileType: string;
    mimeType: string;
    capturedAt: Date;
    latitude?: number;
    longitude?: number;
    locationName?: string;
    additionalMetadata?: object;
    createdAt: Date;
    updatedAt: Date;
}

export const StorageMetadataSchema: Schema = new Schema(
    {
        userId: { type: Schema.Types.ObjectId, required: true, ref: 'user', index: 1 },
        fileId: { type: String, required: true, index: 1 },
        fileName: { type: String, required: true },
        filePath: { type: String, required: true },
        fileSize: { type: Number, required: true, index: 1 },
        fileType: { type: String, required: true, index: 1 },
        mimeType: { type: String, required: true },
        capturedAt: { type: Date, required: true, index: 1 },
        latitude: { type: Number },
        longitude: { type: Number },
        locationName: { type: String },
        additionalMetadata: { type: Object },
        createdAt: { type: Date },
        updatedAt: { type: Date }
    },
    {
        timestamps: true,
    },
);

// Add compound indexes for efficient queries
StorageMetadataSchema.index({ userId: 1, fileType: 1 });
StorageMetadataSchema.index({ userId: 1, capturedAt: -1 });
StorageMetadataSchema.index({ userId: 1, fileSize: -1 });

StorageMetadataSchema.plugin(aggregatePaginate);
