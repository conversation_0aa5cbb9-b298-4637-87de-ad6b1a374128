/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */
import { Schema, Document } from "mongoose";

export interface IStorageQuota extends Document {
    userId: string;
    totalQuotaBytes: number;
    usedBytes: number;
    usageByType: {
        image: number;
        video: number;
        audio: number;
        document: number;
    };
    isPremium: boolean;
    premiumExpiresAt?: Date;
    lastCalculatedAt: Date;
    createdAt: Date;
    updatedAt: Date;
}

export const StorageQuotaSchema: Schema = new Schema(
    {
        userId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'user', 
            unique: true, 
            index: 1 
        },
        totalQuotaBytes: { 
            type: Number, 
            required: true, 
            default: 1024 * 1024 * 1024 // 1GB default for free users
        },
        usedBytes: { 
            type: Number, 
            required: true, 
            default: 0,
            index: 1 
        },
        usageByType: {
            image: { type: Number, default: 0 },
            video: { type: Number, default: 0 },
            audio: { type: Number, default: 0 },
            document: { type: Number, default: 0 }
        },
        isPremium: { 
            type: Boolean, 
            default: false,
            index: 1 
        },
        premiumExpiresAt: { 
            type: Date,
            index: 1 
        },
        lastCalculatedAt: { 
            type: Date, 
            default: Date.now,
            index: 1 
        },
        createdAt: { type: Date },
        updatedAt: { type: Date }
    },
    {
        timestamps: true,
    },
);

// Virtual for available bytes
StorageQuotaSchema.virtual('availableBytes').get(function() {
    return this.totalQuotaBytes - this.usedBytes;
});

// Virtual for usage percentage
StorageQuotaSchema.virtual('usagePercentage').get(function() {
    return this.usedBytes / this.totalQuotaBytes;
});

// Virtual for checking if near limit
StorageQuotaSchema.virtual('isNearLimit').get(function() {
    return this.usagePercentage > 0.8;
});

// Virtual for checking if over limit
StorageQuotaSchema.virtual('isOverLimit').get(function() {
    return this.usedBytes > this.totalQuotaBytes;
});

// Method to update usage
StorageQuotaSchema.methods.updateUsage = function(fileSize: number, fileType: string, isAdding: boolean = true) {
    const multiplier = isAdding ? 1 : -1;
    this.usedBytes += fileSize * multiplier;
    
    // Update usage by type
    const typeKey = this.getFileTypeKey(fileType);
    if (this.usageByType[typeKey] !== undefined) {
        this.usageByType[typeKey] += fileSize * multiplier;
    }
    
    this.lastCalculatedAt = new Date();
};

// Method to get file type key
StorageQuotaSchema.methods.getFileTypeKey = function(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    return 'document';
};

// Method to upgrade to premium
StorageQuotaSchema.methods.upgradeToPremium = function(expiresAt?: Date) {
    this.isPremium = true;
    this.totalQuotaBytes = 10 * 1024 * 1024 * 1024; // 10GB for premium
    this.premiumExpiresAt = expiresAt;
};

// Method to check if premium is expired
StorageQuotaSchema.methods.checkPremiumExpiry = function() {
    if (this.isPremium && this.premiumExpiresAt && new Date() > this.premiumExpiresAt) {
        this.isPremium = false;
        this.totalQuotaBytes = 1024 * 1024 * 1024; // Back to 1GB
        this.premiumExpiresAt = undefined;
        return true; // Premium expired
    }
    return false; // Premium still valid or not premium
};
