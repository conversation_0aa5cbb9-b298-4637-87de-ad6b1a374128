/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */
import { Schema, Document } from "mongoose";
import aggregatePaginate from "mongoose-aggregate-paginate-v2";

export enum ReminderType {
    OLD_FILE = 'oldFile',
    STORAGE_LIMIT = 'storageLimit',
    CUSTOM = 'custom'
}

export interface IFileReminder extends Document {
    userId: string;
    fileId: string;
    fileName: string;
    fileSize: number;
    fileCreatedAt: Date;
    reminderType: ReminderType;
    reminderDate: Date;
    isActive: boolean;
    isCompleted: boolean;
    customMessage?: string;
    createdAt: Date;
    updatedAt: Date;
}

export const FileReminderSchema: Schema = new Schema(
    {
        userId: { 
            type: Schema.Types.ObjectId, 
            required: true, 
            ref: 'user', 
            index: 1 
        },
        fileId: { 
            type: String, 
            required: true, 
            index: 1 
        },
        fileName: { 
            type: String, 
            required: true 
        },
        fileSize: { 
            type: Number, 
            required: true 
        },
        fileCreatedAt: { 
            type: Date, 
            required: true,
            index: 1 
        },
        reminderType: { 
            type: String, 
            enum: Object.values(ReminderType),
            required: true,
            index: 1 
        },
        reminderDate: { 
            type: Date, 
            required: true,
            index: 1 
        },
        isActive: { 
            type: Boolean, 
            default: true,
            index: 1 
        },
        isCompleted: { 
            type: Boolean, 
            default: false,
            index: 1 
        },
        customMessage: { 
            type: String 
        },
        createdAt: { type: Date },
        updatedAt: { type: Date }
    },
    {
        timestamps: true,
    },
);

// Add compound indexes for efficient queries
FileReminderSchema.index({ userId: 1, isActive: 1 });
FileReminderSchema.index({ userId: 1, reminderDate: 1 });
FileReminderSchema.index({ userId: 1, reminderType: 1 });
FileReminderSchema.index({ userId: 1, isCompleted: 1 });

// Virtual for checking if reminder is due
FileReminderSchema.virtual('isDue').get(function() {
    return new Date() >= this.reminderDate;
});

// Virtual for checking if reminder is overdue
FileReminderSchema.virtual('isOverdue').get(function() {
    return this.isDue && !this.isCompleted;
});

// Method to get default message based on reminder type
FileReminderSchema.methods.getDefaultMessage = function(): string {
    switch (this.reminderType) {
        case ReminderType.OLD_FILE:
            return `This file is ${this.getFileAge()} old. Consider deleting it to free up space.`;
        case ReminderType.STORAGE_LIMIT:
            return 'Your storage is almost full. Consider deleting this file.';
        case ReminderType.CUSTOM:
            return this.customMessage || 'File reminder';
        default:
            return 'File reminder';
    }
};

// Method to calculate file age
FileReminderSchema.methods.getFileAge = function(): string {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - this.fileCreatedAt.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
        const years = Math.floor(diffDays / 365);
        return `${years} year${years > 1 ? 's' : ''}`;
    } else if (diffDays > 30) {
        const months = Math.floor(diffDays / 30);
        return `${months} month${months > 1 ? 's' : ''}`;
    } else if (diffDays > 0) {
        return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
    } else {
        return 'less than a day';
    }
};

// Static method to create automatic reminders for old files
FileReminderSchema.statics.createOldFileReminder = function(userId: string, fileId: string, fileName: string, fileSize: number, fileCreatedAt: Date) {
    const reminderDate = new Date();
    reminderDate.setDate(reminderDate.getDate() + 30); // Remind in 30 days
    
    return new this({
        userId,
        fileId,
        fileName,
        fileSize,
        fileCreatedAt,
        reminderType: ReminderType.OLD_FILE,
        reminderDate,
        isActive: true,
        isCompleted: false
    });
};

FileReminderSchema.plugin(aggregatePaginate);
