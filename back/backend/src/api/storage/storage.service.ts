/**
 * Copyright 2023, the hate<PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */
import { Injectable, BadRequestException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { IStorageMetadata } from "./entities/storage_metadata.entity";
import { IStorageQuota } from "./entities/storage_quota.entity";
import { IFileReminder } from "./entities/file_reminder.entity";
import { IUser } from "../user_modules/user/entities/user.entity";
import { 
    CreateStorageMetadataDto, 
    CreateFileReminderDto, 
    UpdateFileReminderDto,
    UpgradeToPremiumDto,
    StorageUsageQueryDto,
    FileRemindersQueryDto
} from "./dto/storage.dto";

@Injectable()
export class StorageService {
    constructor(
        @InjectModel("storage_metadata") private storageMetadataModel: Model<IStorageMetadata>,
        @InjectModel("storage_quota") private storageQuotaModel: Model<IStorageQuota>,
        @InjectModel("file_reminder") private fileReminderModel: Model<IFileReminder>,
    ) {}

    // Storage Metadata Methods
    async createStorageMetadata(dto: CreateStorageMetadataDto): Promise<IStorageMetadata> {
        const metadata = new this.storageMetadataModel(dto.toJson());
        const savedMetadata = await metadata.save();

        // Update storage quota
        await this.updateStorageUsage(dto.myUser._id, dto.fileSize, dto.mimeType, true);

        return savedMetadata;
    }

    async getStorageMetadata(userId: string, query: StorageUsageQueryDto): Promise<any> {
        const filter: any = { userId };
        
        if (query.fileType) {
            filter.fileType = query.fileType;
        }
        
        if (query.startDate || query.endDate) {
            filter.capturedAt = {};
            if (query.startDate) filter.capturedAt.$gte = new Date(query.startDate);
            if (query.endDate) filter.capturedAt.$lte = new Date(query.endDate);
        }

        const page = query.page || 1;
        const limit = query.limit || 20;
        const skip = (page - 1) * limit;

        const [metadata, total] = await Promise.all([
            this.storageMetadataModel
                .find(filter)
                .sort({ capturedAt: -1 })
                .skip(skip)
                .limit(limit)
                .exec(),
            this.storageMetadataModel.countDocuments(filter)
        ]);

        return {
            data: metadata,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        };
    }

    async deleteStorageMetadata(userId: string, metadataId: string): Promise<void> {
        const metadata = await this.storageMetadataModel.findOne({ 
            _id: metadataId, 
            userId 
        });
        
        if (!metadata) {
            throw new NotFoundException("Storage metadata not found");
        }

        // Update storage quota (subtract the file size)
        await this.updateStorageUsage(userId, metadata.fileSize, metadata.mimeType, false);

        await this.storageMetadataModel.deleteOne({ _id: metadataId, userId });
    }

    // Storage Quota Methods
    async getStorageQuota(userId: string): Promise<IStorageQuota> {
        let quota = await this.storageQuotaModel.findOne({ userId });
        
        if (!quota) {
            quota = await this.createDefaultQuota(userId);
        }

        // Check if premium has expired
        if (quota.checkPremiumExpiry()) {
            await quota.save();
        }

        return quota;
    }

    async updateStorageUsage(userId: string, fileSize: number, mimeType: string, isAdding: boolean = true): Promise<IStorageQuota> {
        let quota = await this.storageQuotaModel.findOne({ userId });
        
        if (!quota) {
            quota = await this.createDefaultQuota(userId);
        }

        quota.updateUsage(fileSize, mimeType, isAdding);
        return await quota.save();
    }

    async upgradeToPremium(dto: UpgradeToPremiumDto): Promise<IStorageQuota> {
        let quota = await this.storageQuotaModel.findOne({ userId: dto.myUser._id });
        
        if (!quota) {
            quota = await this.createDefaultQuota(dto.myUser._id);
        }

        // Calculate expiry date based on plan type
        const expiresAt = new Date();
        if (dto.planType === 'monthly') {
            expiresAt.setMonth(expiresAt.getMonth() + 1);
        } else if (dto.planType === 'yearly') {
            expiresAt.setFullYear(expiresAt.getFullYear() + 1);
        } else {
            throw new BadRequestException("Invalid plan type");
        }

        quota.upgradeToPremium(expiresAt);
        return await quota.save();
    }

    private async createDefaultQuota(userId: string): Promise<IStorageQuota> {
        const quota = new this.storageQuotaModel({
            userId,
            totalQuotaBytes: 1024 * 1024 * 1024, // 1GB
            usedBytes: 0,
            usageByType: {
                image: 0,
                video: 0,
                audio: 0,
                document: 0
            },
            isPremium: false,
            lastCalculatedAt: new Date()
        });
        
        return await quota.save();
    }

    // File Reminder Methods
    async createFileReminder(dto: CreateFileReminderDto): Promise<IFileReminder> {
        const reminder = new this.fileReminderModel(dto.toJson());
        return await reminder.save();
    }

    async getFileReminders(userId: string, query: FileRemindersQueryDto): Promise<any> {
        const filter: any = { userId };
        
        if (query.isActive !== undefined) filter.isActive = query.isActive;
        if (query.isCompleted !== undefined) filter.isCompleted = query.isCompleted;
        if (query.reminderType) filter.reminderType = query.reminderType;

        const page = query.page || 1;
        const limit = query.limit || 20;
        const skip = (page - 1) * limit;

        const [reminders, total] = await Promise.all([
            this.fileReminderModel
                .find(filter)
                .sort({ reminderDate: 1 })
                .skip(skip)
                .limit(limit)
                .exec(),
            this.fileReminderModel.countDocuments(filter)
        ]);

        return {
            data: reminders,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        };
    }

    async updateFileReminder(userId: string, reminderId: string, dto: UpdateFileReminderDto): Promise<IFileReminder> {
        const reminder = await this.fileReminderModel.findOne({ 
            _id: reminderId, 
            userId 
        });
        
        if (!reminder) {
            throw new NotFoundException("File reminder not found");
        }

        Object.assign(reminder, dto.toJson());
        return await reminder.save();
    }

    async deleteFileReminder(userId: string, reminderId: string): Promise<void> {
        const result = await this.fileReminderModel.deleteOne({ 
            _id: reminderId, 
            userId 
        });
        
        if (result.deletedCount === 0) {
            throw new NotFoundException("File reminder not found");
        }
    }

    async getDueReminders(userId: string): Promise<IFileReminder[]> {
        return await this.fileReminderModel.find({
            userId,
            isActive: true,
            isCompleted: false,
            reminderDate: { $lte: new Date() }
        }).sort({ reminderDate: 1 });
    }

    // Analytics Methods
    async getStorageAnalytics(userId: string): Promise<any> {
        const quota = await this.getStorageQuota(userId);
        
        const [totalFiles, oldestFile, newestFile] = await Promise.all([
            this.storageMetadataModel.countDocuments({ userId }),
            this.storageMetadataModel.findOne({ userId }).sort({ capturedAt: 1 }),
            this.storageMetadataModel.findOne({ userId }).sort({ capturedAt: -1 })
        ]);

        return {
            quota: {
                totalQuotaBytes: quota.totalQuotaBytes,
                usedBytes: quota.usedBytes,
                availableBytes: quota.availableBytes,
                usagePercentage: quota.usagePercentage,
                isPremium: quota.isPremium,
                isNearLimit: quota.isNearLimit,
                isOverLimit: quota.isOverLimit
            },
            usageByType: quota.usageByType,
            totalFiles,
            oldestFile: oldestFile?.capturedAt,
            newestFile: newestFile?.capturedAt
        };
    }
}
