/**
 * Copyright 2023, the hate<PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */
import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    Req
} from "@nestjs/common";
import { StorageService } from "./storage.service";
import { VerifiedAuthGuard } from "../../core/guards/verified.auth.guard";
import { resOK } from "../../core/utils/res.helpers";
import { V1Controller } from "../../core/common/v1-controller.decorator";
import { MongoIdDto } from "../../core/common/dto/mongo.id.dto";
import {
    CreateStorageMetadataDto,
    CreateFileReminderDto,
    UpdateFileReminderDto,
    UpgradeToPremiumDto,
    StorageUsageQueryDto,
    FileRemindersQueryDto
} from "./dto/storage.dto";

@V1Controller("storage")
export class StorageController {
    constructor(private readonly storageService: StorageService) {}

    // Storage Metadata Endpoints
    @UseGuards(VerifiedAuthGuard)
    @Post("/metadata")
    async createStorageMetadata(@Req() req: any, @Body() dto: CreateStorageMetadataDto) {
        (dto as any).myUser = req.user;
        return resOK(await this.storageService.createStorageMetadata(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/metadata")
    async getStorageMetadata(@Req() req: any, @Query() query: StorageUsageQueryDto) {
        return resOK(await this.storageService.getStorageMetadata(req.user._id, query));
    }

    @UseGuards(VerifiedAuthGuard)
    @Delete("/metadata/:id")
    async deleteStorageMetadata(@Req() req: any, @Param() params: MongoIdDto) {
        await this.storageService.deleteStorageMetadata(req.user._id, params.id);
        return resOK({ message: "Storage metadata deleted successfully" });
    }

    @UseGuards(VerifiedAuthGuard)
    @Delete("/file/:filePath")
    async deleteAllMetadataForFile(@Req() req: any, @Param("filePath") filePath: string) {
        // Decode the file path since it might contain special characters
        const decodedFilePath = decodeURIComponent(filePath);
        await this.storageService.deleteAllMetadataForFile(req.user._id, decodedFilePath);
        return resOK({ message: "File and all metadata deleted successfully" });
    }

    // Storage Quota Endpoints
    @UseGuards(VerifiedAuthGuard)
    @Get("/quota")
    async getStorageQuota(@Req() req: any) {
        return resOK(await this.storageService.getStorageQuota(req.user._id));
    }

    @UseGuards(VerifiedAuthGuard)
    @Post("/upgrade-premium")
    async upgradeToPremium(@Req() req: any, @Body() dto: UpgradeToPremiumDto) {
        (dto as any).myUser = req.user;
        return resOK(await this.storageService.upgradeToPremium(dto));
    }

    // File Reminder Endpoints
    @UseGuards(VerifiedAuthGuard)
    @Post("/reminders")
    async createFileReminder(@Req() req: any, @Body() dto: CreateFileReminderDto) {
        (dto as any).myUser = req.user;
        return resOK(await this.storageService.createFileReminder(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/reminders")
    async getFileReminders(@Req() req: any, @Query() query: FileRemindersQueryDto) {
        return resOK(await this.storageService.getFileReminders(req.user._id, query));
    }

    @UseGuards(VerifiedAuthGuard)
    @Put("/reminders/:id")
    async updateFileReminder(
        @Req() req: any, 
        @Param() params: MongoIdDto, 
        @Body() dto: UpdateFileReminderDto
    ) {
        (dto as any).myUser = req.user;
        return resOK(await this.storageService.updateFileReminder(req.user._id, params.id, dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Delete("/reminders/:id")
    async deleteFileReminder(@Req() req: any, @Param() params: MongoIdDto) {
        await this.storageService.deleteFileReminder(req.user._id, params.id);
        return resOK({ message: "File reminder deleted successfully" });
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/reminders/due")
    async getDueReminders(@Req() req: any) {
        return resOK(await this.storageService.getDueReminders(req.user._id));
    }

    // Analytics Endpoints
    @UseGuards(VerifiedAuthGuard)
    @Get("/analytics")
    async getStorageAnalytics(@Req() req: any) {
        return resOK(await this.storageService.getStorageAnalytics(req.user._id));
    }
}
