/**
 * Copyright 2023, the <PERSON><PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsDateString, IsBoolean, IsEnum, IsObject } from "class-validator";
import { CommonDto } from "../../../core/common/dto/common.dto";
import { ReminderType } from "../entities/file_reminder.entity";

export class CreateStorageMetadataDto extends CommonDto {
    @IsNotEmpty()
    @IsString()
    fileId: string;

    @IsNotEmpty()
    @IsString()
    fileName: string;

    @IsNotEmpty()
    @IsString()
    filePath: string;

    @IsNotEmpty()
    @IsNumber()
    fileSize: number;

    @IsNotEmpty()
    @IsString()
    fileType: string;

    @IsNotEmpty()
    @IsString()
    mimeType: string;

    @IsNotEmpty()
    @IsDateString()
    capturedAt: string;

    @IsOptional()
    @IsNumber()
    latitude?: number;

    @IsOptional()
    @IsNumber()
    longitude?: number;

    @IsOptional()
    @IsString()
    locationName?: string;

    @IsOptional()
    @IsObject()
    additionalMetadata?: object;

    toJson() {
        return {
            userId: this.myUser._id,
            fileId: this.fileId,
            fileName: this.fileName,
            filePath: this.filePath,
            fileSize: this.fileSize,
            fileType: this.fileType,
            mimeType: this.mimeType,
            capturedAt: new Date(this.capturedAt),
            latitude: this.latitude,
            longitude: this.longitude,
            locationName: this.locationName,
            additionalMetadata: this.additionalMetadata,
        };
    }
}

export class CreateFileReminderDto extends CommonDto {
    @IsNotEmpty()
    @IsString()
    fileId: string;

    @IsNotEmpty()
    @IsString()
    fileName: string;

    @IsNotEmpty()
    @IsNumber()
    fileSize: number;

    @IsNotEmpty()
    @IsDateString()
    fileCreatedAt: string;

    @IsNotEmpty()
    @IsEnum(ReminderType)
    reminderType: ReminderType;

    @IsNotEmpty()
    @IsDateString()
    reminderDate: string;

    @IsOptional()
    @IsString()
    customMessage?: string;

    toJson() {
        return {
            userId: this.myUser._id,
            fileId: this.fileId,
            fileName: this.fileName,
            fileSize: this.fileSize,
            fileCreatedAt: new Date(this.fileCreatedAt),
            reminderType: this.reminderType,
            reminderDate: new Date(this.reminderDate),
            customMessage: this.customMessage,
            isActive: true,
            isCompleted: false,
        };
    }
}

export class UpdateFileReminderDto extends CommonDto {
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @IsOptional()
    @IsBoolean()
    isCompleted?: boolean;

    @IsOptional()
    @IsDateString()
    reminderDate?: string;

    @IsOptional()
    @IsString()
    customMessage?: string;

    toJson() {
        const data: any = {};
        if (this.isActive !== undefined) data.isActive = this.isActive;
        if (this.isCompleted !== undefined) data.isCompleted = this.isCompleted;
        if (this.reminderDate) data.reminderDate = new Date(this.reminderDate);
        if (this.customMessage !== undefined) data.customMessage = this.customMessage;
        return data;
    }
}

export class UpgradeToPremiumDto extends CommonDto {
    @IsNotEmpty()
    @IsString()
    planType: string; // 'monthly' | 'yearly'

    @IsOptional()
    @IsString()
    paymentMethodId?: string;

    @IsOptional()
    @IsObject()
    paymentMetadata?: object;

    toJson() {
        return {
            userId: this.myUser._id,
            planType: this.planType,
            paymentMethodId: this.paymentMethodId,
            paymentMetadata: this.paymentMetadata,
        };
    }
}

export class StorageUsageQueryDto {
    @IsOptional()
    @IsString()
    fileType?: string;

    @IsOptional()
    @IsDateString()
    startDate?: string;

    @IsOptional()
    @IsDateString()
    endDate?: string;

    @IsOptional()
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    limit?: number = 20;
}

export class FileRemindersQueryDto {
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @IsOptional()
    @IsBoolean()
    isCompleted?: boolean;

    @IsOptional()
    @IsEnum(ReminderType)
    reminderType?: ReminderType;

    @IsOptional()
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    limit?: number = 20;
}
