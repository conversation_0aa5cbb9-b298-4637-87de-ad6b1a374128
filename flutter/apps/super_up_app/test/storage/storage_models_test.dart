// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter_test/flutter_test.dart';
import 'package:super_up/app/core/models/storage/storage_quota_model.dart';
import 'package:super_up/app/core/models/storage/storage_metadata_model.dart';
import 'package:super_up/app/core/models/storage/file_reminder_model.dart';

void main() {
  group('Storage Models Tests', () {
    group('StorageQuotaModel', () {
      test('should calculate usage percentage correctly', () {
        // Arrange
        final quota = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 1000,
          usedBytes: 500,
          usageByType: {},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );

        // Act & Assert
        expect(quota.usagePercentage, equals(0.5));
        expect(quota.availableBytes, equals(500));
      });

      test('should detect near limit correctly', () {
        // Arrange
        final quota = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 1000,
          usedBytes: 850, // 85%
          usageByType: {},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );

        // Act & Assert
        expect(quota.isNearLimit, isTrue);
        expect(quota.isOverLimit, isFalse);
      });

      test('should detect over limit correctly', () {
        // Arrange
        final quota = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 1000,
          usedBytes: 1100, // 110%
          usageByType: {},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );

        // Act & Assert
        expect(quota.isOverLimit, isTrue);
        expect(quota.isNearLimit, isTrue); // Over limit implies near limit
      });

      test('should format readable sizes correctly', () {
        // Test bytes
        final quotaBytes = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 512,
          usedBytes: 256,
          usageByType: {},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );
        expect(quotaBytes.totalQuotaReadable, equals('512B'));
        expect(quotaBytes.usedReadable, equals('256B'));

        // Test KB
        final quotaKB = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 2048, // 2KB
          usedBytes: 1024, // 1KB
          usageByType: {},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );
        expect(quotaKB.totalQuotaReadable, equals('2.0KB'));
        expect(quotaKB.usedReadable, equals('1.0KB'));

        // Test MB
        final quotaMB = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 2 * 1024 * 1024, // 2MB
          usedBytes: 1024 * 1024, // 1MB
          usageByType: {},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );
        expect(quotaMB.totalQuotaReadable, equals('2.0MB'));
        expect(quotaMB.usedReadable, equals('1.0MB'));

        // Test GB
        final quotaGB = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 2 * 1024 * 1024 * 1024, // 2GB
          usedBytes: 1024 * 1024 * 1024, // 1GB
          usageByType: {},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );
        expect(quotaGB.totalQuotaReadable, equals('2.0GB'));
        expect(quotaGB.usedReadable, equals('1.0GB'));
      });

      test('should serialize to/from JSON correctly', () {
        // Arrange
        final originalQuota = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 1024 * 1024 * 1024,
          usedBytes: 512 * 1024 * 1024,
          usageByType: {
            'image': 256 * 1024 * 1024,
            'video': 128 * 1024 * 1024,
            'audio': 64 * 1024 * 1024,
            'document': 64 * 1024 * 1024,
          },
          isPremium: true,
          premiumExpiresAt: DateTime(2024, 12, 31),
          lastCalculatedAt: DateTime(2024, 1, 1),
        );

        // Act
        final json = originalQuota.toJson();
        final deserializedQuota = StorageQuotaModel.fromJson(json);

        // Assert
        expect(deserializedQuota.userId, equals(originalQuota.userId));
        expect(deserializedQuota.totalQuotaBytes, equals(originalQuota.totalQuotaBytes));
        expect(deserializedQuota.usedBytes, equals(originalQuota.usedBytes));
        expect(deserializedQuota.usageByType, equals(originalQuota.usageByType));
        expect(deserializedQuota.isPremium, equals(originalQuota.isPremium));
        expect(deserializedQuota.premiumExpiresAt, equals(originalQuota.premiumExpiresAt));
        expect(deserializedQuota.lastCalculatedAt, equals(originalQuota.lastCalculatedAt));
      });
    });

    group('StorageMetadataModel', () {
      test('should determine file type correctly', () {
        // Test image
        final imageFile = StorageMetadataModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          filePath: '/path/to/test.jpg',
          fileSize: 1024,
          fileType: 'image',
          mimeType: 'image/jpeg',
          capturedAt: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        expect(imageFile.storageFileType, equals(StorageFileType.image));

        // Test video
        final videoFile = StorageMetadataModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.mp4',
          filePath: '/path/to/test.mp4',
          fileSize: 1024 * 1024,
          fileType: 'video',
          mimeType: 'video/mp4',
          capturedAt: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        expect(videoFile.storageFileType, equals(StorageFileType.video));

        // Test audio
        final audioFile = StorageMetadataModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.mp3',
          filePath: '/path/to/test.mp3',
          fileSize: 1024 * 512,
          fileType: 'audio',
          mimeType: 'audio/mpeg',
          capturedAt: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        expect(audioFile.storageFileType, equals(StorageFileType.audio));

        // Test document
        final documentFile = StorageMetadataModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.pdf',
          filePath: '/path/to/test.pdf',
          fileSize: 1024 * 256,
          fileType: 'document',
          mimeType: 'application/pdf',
          capturedAt: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        expect(documentFile.storageFileType, equals(StorageFileType.document));
      });

      test('should detect location availability', () {
        // With location
        final fileWithLocation = StorageMetadataModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          filePath: '/path/to/test.jpg',
          fileSize: 1024,
          fileType: 'image',
          mimeType: 'image/jpeg',
          capturedAt: DateTime.now(),
          latitude: 37.7749,
          longitude: -122.4194,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        expect(fileWithLocation.hasLocation, isTrue);

        // Without location
        final fileWithoutLocation = StorageMetadataModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          filePath: '/path/to/test.jpg',
          fileSize: 1024,
          fileType: 'image',
          mimeType: 'image/jpeg',
          capturedAt: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        expect(fileWithoutLocation.hasLocation, isFalse);
      });
    });

    group('FileReminderModel', () {
      test('should detect due and overdue reminders correctly', () {
        final now = DateTime.now();

        // Future reminder (not due)
        final futureReminder = FileReminderModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          fileSize: 1024,
          fileCreatedAt: now.subtract(Duration(days: 30)),
          reminderType: ReminderType.oldFile,
          reminderDate: now.add(Duration(hours: 1)),
          isActive: true,
          isCompleted: false,
          createdAt: now,
          updatedAt: now,
        );
        expect(futureReminder.isDue, isFalse);
        expect(futureReminder.isOverdue, isFalse);

        // Due reminder (exactly now)
        final dueReminder = FileReminderModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          fileSize: 1024,
          fileCreatedAt: now.subtract(Duration(days: 30)),
          reminderType: ReminderType.oldFile,
          reminderDate: now,
          isActive: true,
          isCompleted: false,
          createdAt: now,
          updatedAt: now,
        );
        expect(dueReminder.isDue, isTrue);
        expect(dueReminder.isOverdue, isTrue);

        // Overdue reminder
        final overdueReminder = FileReminderModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          fileSize: 1024,
          fileCreatedAt: now.subtract(Duration(days: 30)),
          reminderType: ReminderType.oldFile,
          reminderDate: now.subtract(Duration(hours: 1)),
          isActive: true,
          isCompleted: false,
          createdAt: now,
          updatedAt: now,
        );
        expect(overdueReminder.isDue, isTrue);
        expect(overdueReminder.isOverdue, isTrue);

        // Completed reminder (not overdue even if past due date)
        final completedReminder = FileReminderModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          fileSize: 1024,
          fileCreatedAt: now.subtract(Duration(days: 30)),
          reminderType: ReminderType.oldFile,
          reminderDate: now.subtract(Duration(hours: 1)),
          isActive: true,
          isCompleted: true,
          createdAt: now,
          updatedAt: now,
        );
        expect(completedReminder.isDue, isTrue);
        expect(completedReminder.isOverdue, isFalse); // Completed, so not overdue
      });

      test('should generate correct default messages', () {
        final now = DateTime.now();

        // Old file reminder
        final oldFileReminder = FileReminderModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          fileSize: 1024,
          fileCreatedAt: now.subtract(Duration(days: 30)),
          reminderType: ReminderType.oldFile,
          reminderDate: now.add(Duration(days: 7)),
          isActive: true,
          isCompleted: false,
          createdAt: now,
          updatedAt: now,
        );
        expect(oldFileReminder.defaultMessage, contains('old'));
        expect(oldFileReminder.defaultMessage, contains('free up space'));

        // Storage limit reminder
        final storageLimitReminder = FileReminderModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          fileSize: 1024,
          fileCreatedAt: now.subtract(Duration(days: 30)),
          reminderType: ReminderType.storageLimit,
          reminderDate: now.add(Duration(days: 7)),
          isActive: true,
          isCompleted: false,
          createdAt: now,
          updatedAt: now,
        );
        expect(storageLimitReminder.defaultMessage, contains('storage'));
        expect(storageLimitReminder.defaultMessage, contains('almost full'));

        // Custom reminder
        final customReminder = FileReminderModel(
          id: 'test-id',
          userId: 'test-user',
          fileId: 'file-id',
          fileName: 'test.jpg',
          fileSize: 1024,
          fileCreatedAt: now.subtract(Duration(days: 30)),
          reminderType: ReminderType.custom,
          reminderDate: now.add(Duration(days: 7)),
          isActive: true,
          isCompleted: false,
          customMessage: 'Custom reminder message',
          createdAt: now,
          updatedAt: now,
        );
        expect(customReminder.defaultMessage, equals('Custom reminder message'));
      });
    });
  });
}
