// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:super_up/app/core/api_service/storage/storage_api_service.dart';
import 'package:super_up/app/core/models/storage/storage_quota_model.dart';
import 'package:super_up/app/core/models/storage/storage_metadata_model.dart';
import 'package:super_up/app/core/models/storage/file_reminder_model.dart';
import 'package:super_up/app/core/models/storage/storage_dto.dart';
import 'package:super_up/app/modules/storage/controllers/storage_controller.dart';

// Generate mocks
@GenerateMocks([StorageApiService])
import 'storage_controller_test.mocks.dart';

void main() {
  group('StorageController Tests', () {
    late StorageController controller;
    late MockStorageApiService mockApiService;

    setUp(() {
      mockApiService = MockStorageApiService();
      controller = StorageController();
      // In a real test, you'd inject the mock service
    });

    tearDown(() {
      controller.onClose();
    });

    group('Storage Quota Management', () {
      test('should load storage quota successfully', () async {
        // Arrange
        final mockQuota = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 1024 * 1024 * 1024, // 1GB
          usedBytes: 512 * 1024 * 1024, // 512MB
          usageByType: {
            'image': 256 * 1024 * 1024,
            'video': 128 * 1024 * 1024,
            'audio': 64 * 1024 * 1024,
            'document': 64 * 1024 * 1024,
          },
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );

        when(mockApiService.getStorageQuota())
            .thenAnswer((_) async => mockQuota);

        // Act
        await controller.loadStorageQuota();

        // Assert
        expect(controller.hasStorageQuota, isTrue);
        expect(controller.isPremium, isFalse);
        expect(controller.storageUsagePercentage, equals(0.5));
        expect(controller.isNearStorageLimit, isFalse);
        expect(controller.isOverStorageLimit, isFalse);
      });

      test('should detect near storage limit', () async {
        // Arrange
        final mockQuota = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 1024 * 1024 * 1024, // 1GB
          usedBytes: 900 * 1024 * 1024, // 900MB (87.5%)
          usageByType: {'image': 900 * 1024 * 1024},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );

        when(mockApiService.getStorageQuota())
            .thenAnswer((_) async => mockQuota);

        // Act
        await controller.loadStorageQuota();

        // Assert
        expect(controller.isNearStorageLimit, isTrue);
        expect(controller.isOverStorageLimit, isFalse);
      });

      test('should detect over storage limit', () async {
        // Arrange
        final mockQuota = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 1024 * 1024 * 1024, // 1GB
          usedBytes: 1100 * 1024 * 1024, // 1.1GB (over limit)
          usageByType: {'image': 1100 * 1024 * 1024},
          isPremium: false,
          lastCalculatedAt: DateTime.now(),
        );

        when(mockApiService.getStorageQuota())
            .thenAnswer((_) async => mockQuota);

        // Act
        await controller.loadStorageQuota();

        // Assert
        expect(controller.isOverStorageLimit, isTrue);
      });
    });

    group('File Reminders Management', () {
      test('should create file reminder successfully', () async {
        // Arrange
        final reminderDto = CreateFileReminderDto(
          fileId: 'test-file-id',
          fileName: 'test-file.jpg',
          fileSize: 1024 * 1024, // 1MB
          fileCreatedAt: DateTime.now().subtract(Duration(days: 30)),
          reminderType: 'oldFile',
          reminderDate: DateTime.now().add(Duration(days: 7)),
        );

        final mockReminder = FileReminderModel(
          id: 'reminder-id',
          userId: 'test-user',
          fileId: reminderDto.fileId,
          fileName: reminderDto.fileName,
          fileSize: reminderDto.fileSize,
          fileCreatedAt: reminderDto.fileCreatedAt,
          reminderType: ReminderType.oldFile,
          reminderDate: reminderDto.reminderDate,
          isActive: true,
          isCompleted: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockApiService.createFileReminder(reminderDto))
            .thenAnswer((_) async => mockReminder);

        // Act
        final result = await controller.createFileReminder(reminderDto);

        // Assert
        expect(result, isTrue);
        verify(mockApiService.createFileReminder(reminderDto)).called(1);
      });

      test('should update file reminder successfully', () async {
        // Arrange
        const reminderId = 'reminder-id';
        final updateDto = UpdateFileReminderDto(isCompleted: true);
        
        final mockUpdatedReminder = FileReminderModel(
          id: reminderId,
          userId: 'test-user',
          fileId: 'test-file-id',
          fileName: 'test-file.jpg',
          fileSize: 1024 * 1024,
          fileCreatedAt: DateTime.now().subtract(Duration(days: 30)),
          reminderType: ReminderType.oldFile,
          reminderDate: DateTime.now().add(Duration(days: 7)),
          isActive: true,
          isCompleted: true, // Updated
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockApiService.updateFileReminder(reminderId, updateDto))
            .thenAnswer((_) async => mockUpdatedReminder);

        // Act
        final result = await controller.updateFileReminder(reminderId, updateDto);

        // Assert
        expect(result, isTrue);
        verify(mockApiService.updateFileReminder(reminderId, updateDto)).called(1);
      });

      test('should delete file reminder successfully', () async {
        // Arrange
        const reminderId = 'reminder-id';

        when(mockApiService.deleteFileReminder(reminderId))
            .thenAnswer((_) async => {});

        // Act
        final result = await controller.deleteFileReminder(reminderId);

        // Assert
        expect(result, isTrue);
        verify(mockApiService.deleteFileReminder(reminderId)).called(1);
      });
    });

    group('Premium Upgrade', () {
      test('should upgrade to premium successfully', () async {
        // Arrange
        const planType = 'monthly';
        final mockUpgradedQuota = StorageQuotaModel(
          userId: 'test-user',
          totalQuotaBytes: 10 * 1024 * 1024 * 1024, // 10GB
          usedBytes: 512 * 1024 * 1024, // 512MB
          usageByType: {'image': 512 * 1024 * 1024},
          isPremium: true,
          premiumExpiresAt: DateTime.now().add(Duration(days: 30)),
          lastCalculatedAt: DateTime.now(),
        );

        final upgradeDto = UpgradeToPremiumDto(planType: planType);
        when(mockApiService.upgradeToPremium(upgradeDto))
            .thenAnswer((_) async => mockUpgradedQuota);

        // Act
        final result = await controller.upgradeToPremium(planType);

        // Assert
        expect(result, isTrue);
        expect(controller.isPremium, isTrue);
        verify(mockApiService.upgradeToPremium(any)).called(1);
      });
    });

    group('File Filtering', () {
      test('should filter files by type', () {
        // Arrange
        const fileType = 'image';

        // Act
        controller.filterFilesByType(fileType);

        // Assert
        expect(controller.data.selectedFileType, equals(fileType));
      });

      test('should clear file type filter', () {
        // Arrange
        controller.filterFilesByType('image');

        // Act
        controller.filterFilesByType(null);

        // Assert
        expect(controller.data.selectedFileType, isNull);
      });
    });

    group('Error Handling', () {
      test('should handle storage quota loading error', () async {
        // Arrange
        when(mockApiService.getStorageQuota())
            .thenThrow(Exception('Network error'));

        // Act & Assert
        expect(() => controller.loadStorageQuota(), throwsException);
      });

      test('should handle reminder creation error', () async {
        // Arrange
        final reminderDto = CreateFileReminderDto(
          fileId: 'test-file-id',
          fileName: 'test-file.jpg',
          fileSize: 1024 * 1024,
          fileCreatedAt: DateTime.now(),
          reminderType: 'oldFile',
          reminderDate: DateTime.now().add(Duration(days: 7)),
        );

        when(mockApiService.createFileReminder(reminderDto))
            .thenThrow(Exception('API error'));

        // Act
        final result = await controller.createFileReminder(reminderDto);

        // Assert
        expect(result, isFalse);
        expect(controller.data.error, isNotNull);
      });
    });
  });
}
