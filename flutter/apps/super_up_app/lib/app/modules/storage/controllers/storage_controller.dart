// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:super_up/app/core/api_service/storage/storage_api_service.dart';
import 'package:super_up/app/core/models/storage/storage_metadata_model.dart';
import 'package:super_up/app/core/models/storage/storage_quota_model.dart';
import 'package:super_up/app/core/models/storage/file_reminder_model.dart';
import 'package:super_up/app/core/models/storage/storage_dto.dart';
import 'package:super_up_core/super_up_core.dart';

class StorageState {
  StorageQuotaModel? quota;
  List<StorageMetadataModel> files = [];
  List<FileReminderModel> reminders = [];
  List<FileReminderModel> dueReminders = [];
  Map<String, dynamic>? analytics;
  bool isLoading = false;
  bool hasMoreFiles = true;
  int currentPage = 1;
  String? error;
  String? selectedFileType;
}

class StorageController extends SLoadingController<StorageState> {
  StorageController() : super(SLoadingState(StorageState()));
  
  final _apiService = GetIt.I.get<StorageApiService>();
  Timer? _timer;

  @override
  void onInit() {
    loadStorageData();
    // Refresh data every 5 minutes
    _timer = Timer.periodic(const Duration(minutes: 5), (timer) {
      refreshStorageQuota();
      loadDueReminders();
    });
  }

  @override
  void onClose() {
    _timer?.cancel();
  }

  Future<void> loadStorageData() async {
    data.isLoading = true;
    data.error = null;
    setStateSuccess();
    update();

    try {
      await Future.wait([
        loadStorageQuota(),
        loadFiles(refresh: true),
        loadReminders(refresh: true),
        loadDueReminders(),
        loadAnalytics(),
      ]);

      data.isLoading = false;
      setStateSuccess();
      update();
    } catch (e) {
      data.isLoading = false;
      data.error = e.toString();
      setStateError();
      update();
    }
  }

  Future<void> loadStorageQuota() async {
    try {
      data.quota = await _apiService.getStorageQuota();
      setStateSuccess();
      update();
    } catch (e) {
      print('Error loading storage quota: $e');
    }
  }

  Future<void> refreshStorageQuota() async {
    try {
      data.quota = await _apiService.getStorageQuota();
      setStateSuccess();
      update();
    } catch (e) {
      // Silently handle quota refresh errors
      print('Error refreshing storage quota: $e');
    }
  }

  Future<void> loadFiles({bool refresh = false}) async {
    if (refresh) {
      data.currentPage = 1;
      data.hasMoreFiles = true;
      data.files.clear();
    }

    if (!data.hasMoreFiles) return;

    try {
      final files = await _apiService.getStorageMetadata(
        fileType: data.selectedFileType,
        page: data.currentPage,
        limit: 20,
      );

      if (files.isEmpty) {
        data.hasMoreFiles = false;
      } else {
        data.files.addAll(files);
        data.currentPage++;
      }

      setStateSuccess();
      update();
    } catch (e) {
      data.error = e.toString();
      setStateError();
      update();
    }
  }

  Future<void> loadReminders({bool refresh = false}) async {
    try {
      data.reminders = await _apiService.getFileReminders(
        isActive: true,
        page: 1,
        limit: 50,
      );
      setStateSuccess();
      update();
    } catch (e) {
      print('Error loading reminders: $e');
    }
  }

  Future<void> loadDueReminders() async {
    try {
      data.dueReminders = await _apiService.getDueReminders();
      setStateSuccess();
      update();
    } catch (e) {
      print('Error loading due reminders: $e');
    }
  }

  Future<void> loadAnalytics() async {
    try {
      data.analytics = await _apiService.getStorageAnalytics();
      setStateSuccess();
      update();
    } catch (e) {
      print('Error loading analytics: $e');
    }
  }

  Future<bool> createFileReminder(CreateFileReminderDto dto) async {
    try {
      final reminder = await _apiService.createFileReminder(dto);
      data.reminders.insert(0, reminder);
      setStateSuccess();
      update();
      return true;
    } catch (e) {
      data.error = e.toString();
      setStateError();
      update();
      return false;
    }
  }

  Future<bool> updateFileReminder(String id, UpdateFileReminderDto dto) async {
    try {
      final updatedReminder = await _apiService.updateFileReminder(id, dto);
      final index = data.reminders.indexWhere((r) => r.id == id);
      if (index != -1) {
        data.reminders[index] = updatedReminder;
      }
      
      // Also update due reminders if applicable
      final dueIndex = data.dueReminders.indexWhere((r) => r.id == id);
      if (dueIndex != -1) {
        if (updatedReminder.isCompleted) {
          data.dueReminders.removeAt(dueIndex);
        } else {
          data.dueReminders[dueIndex] = updatedReminder;
        }
      }
      
      setStateSuccess();
      update();
      return true;
    } catch (e) {
      data.error = e.toString();
      setStateError();
      update();
      return false;
    }
  }

  Future<bool> deleteFileReminder(String id) async {
    try {
      await _apiService.deleteFileReminder(id);
      data.reminders.removeWhere((r) => r.id == id);
      data.dueReminders.removeWhere((r) => r.id == id);
      setStateSuccess();
      update();
      return true;
    } catch (e) {
      data.error = e.toString();
      setStateError();
      update();
      return false;
    }
  }

  Future<bool> upgradeToPremium(String planType) async {
    try {
      final dto = UpgradeToPremiumDto(planType: planType);
      data.quota = await _apiService.upgradeToPremium(dto);
      setStateSuccess();
      update();
      return true;
    } catch (e) {
      data.error = e.toString();
      setStateError();
      update();
      return false;
    }
  }

  void filterFilesByType(String? fileType) {
    data.selectedFileType = fileType;
    loadFiles(refresh: true);
  }

  void clearError() {
    data.error = null;
    setStateSuccess();
    update();
  }

  // Helper getters
  bool get hasStorageQuota => data.quota != null;
  bool get isPremium => data.quota?.isPremium ?? false;
  bool get isNearStorageLimit => data.quota?.isNearLimit ?? false;
  bool get isOverStorageLimit => data.quota?.isOverLimit ?? false;
  int get dueRemindersCount => data.dueReminders.length;
  
  String get usedStorageText => data.quota?.usedReadable ?? '0B';
  String get totalStorageText => data.quota?.totalQuotaReadable ?? '1GB';
  double get storageUsagePercentage => data.quota?.usagePercentage ?? 0.0;
}
