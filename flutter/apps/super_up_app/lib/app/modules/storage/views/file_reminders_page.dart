// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up/app/core/models/storage/file_reminder_model.dart';
import 'package:super_up/app/core/models/storage/storage_dto.dart';
import 'package:super_up/app/modules/storage/controllers/storage_controller.dart';
import 'package:super_up/app/modules/storage/views/create_reminder_page.dart';
import 'package:super_up_core/super_up_core.dart';

class FileRemindersPage extends StatefulWidget {
  const FileRemindersPage({super.key});

  @override
  State<FileRemindersPage> createState() => _FileRemindersPageState();
}

class _FileRemindersPageState extends State<FileRemindersPage> {
  late StorageController controller;
  String selectedFilter = 'all'; // 'all', 'active', 'due', 'completed'
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    controller = StorageController();
    controller.onInit();
    _loadData();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  void _loadData() async {
    setState(() => isLoading = true);
    await controller.loadStorageData();
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text('File Reminders'),
            trailing: CupertinoButton(
              padding: EdgeInsets.zero,
              child: Icon(CupertinoIcons.add),
              onPressed: () => _createNewReminder(context),
            ),
          )
        ],
        body: SafeArea(
          top: false,
          child: isLoading
              ? Center(child: CupertinoActivityIndicator())
              : Column(
                  children: [
                    // Filter tabs
                    _buildFilterTabs(context, controller.data),

                    // Due reminders alert
                    if (controller.dueRemindersCount > 0)
                      _buildDueRemindersAlert(context, controller.data),

                    // Reminders list
                    Expanded(
                      child: _buildRemindersList(context, controller.data),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildFilterTabs(BuildContext context, StorageState state) {
    final filters = [
      {'key': 'all', 'name': 'All', 'count': state.reminders.length},
      {
        'key': 'active',
        'name': 'Active',
        'count':
            state.reminders.where((r) => r.isActive && !r.isCompleted).length
      },
      {'key': 'due', 'name': 'Due', 'count': state.dueReminders.length},
      {
        'key': 'completed',
        'name': 'Completed',
        'count': state.reminders.where((r) => r.isCompleted).length
      },
    ];

    return Container(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = selectedFilter == filter['key'];

          return GestureDetector(
            onTap: () =>
                setState(() => selectedFilter = filter['key'] as String),
            child: Container(
              margin: EdgeInsets.only(right: 12),
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? CupertinoColors.systemBlue
                    : CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    filter['name'] as String,
                    style: TextStyle(
                      color: isSelected ? Colors.white : CupertinoColors.label,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  if ((filter['count'] as int) > 0) ...[
                    SizedBox(width: 6),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Colors.white.withValues(alpha: 0.3)
                            : CupertinoColors.systemBlue,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${filter['count']}',
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDueRemindersAlert(BuildContext context, StorageState state) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            CupertinoIcons.exclamationmark_triangle,
            color: Colors.orange,
            size: 20,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${controller.dueRemindersCount} reminder${controller.dueRemindersCount > 1 ? 's' : ''} due',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.orange,
                  ),
                ),
                Text(
                  'Tap to review files that need attention',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ),
          CupertinoButton(
            padding: EdgeInsets.zero,
            child: Text(
              'Review',
              style: TextStyle(color: Colors.orange),
            ),
            onPressed: () => setState(() => selectedFilter = 'due'),
          ),
        ],
      ),
    );
  }

  Widget _buildRemindersList(BuildContext context, StorageState state) {
    final filteredReminders = _getFilteredReminders(state);

    if (state.isLoading && filteredReminders.isEmpty) {
      return Center(child: CupertinoActivityIndicator());
    }

    if (filteredReminders.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredReminders.length,
      itemBuilder: (context, index) {
        final reminder = filteredReminders[index];
        return _buildReminderItem(context, reminder);
      },
    );
  }

  List<FileReminderModel> _getFilteredReminders(StorageState state) {
    switch (selectedFilter) {
      case 'active':
        return state.reminders
            .where((r) => r.isActive && !r.isCompleted)
            .toList();
      case 'due':
        return state.dueReminders;
      case 'completed':
        return state.reminders.where((r) => r.isCompleted).toList();
      default:
        return state.reminders;
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    String title;
    String subtitle;
    IconData icon;

    switch (selectedFilter) {
      case 'active':
        title = 'No Active Reminders';
        subtitle = 'Create reminders to help manage your files';
        icon = CupertinoIcons.bell_slash;
        break;
      case 'due':
        title = 'No Due Reminders';
        subtitle = 'All caught up! No files need attention right now';
        icon = CupertinoIcons.checkmark_circle;
        break;
      case 'completed':
        title = 'No Completed Reminders';
        subtitle = 'Completed reminders will appear here';
        icon = CupertinoIcons.checkmark_alt_circle;
        break;
      default:
        title = 'No Reminders Yet';
        subtitle = 'Create your first reminder to start managing files';
        icon = CupertinoIcons.bell;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: CupertinoColors.systemGrey,
          ),
          SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: CupertinoColors.systemGrey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 16,
              color: CupertinoColors.systemGrey,
            ),
            textAlign: TextAlign.center,
          ),
          if (selectedFilter == 'all') ...[
            SizedBox(height: 24),
            CupertinoButton.filled(
              child: Text('Create Reminder'),
              onPressed: () => _createNewReminder(context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildReminderItem(BuildContext context, FileReminderModel reminder) {
    final isOverdue = reminder.isOverdue;
    final isDue = reminder.isDue && !reminder.isCompleted;

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
        border: isOverdue
            ? Border.all(color: Colors.red.withValues(alpha: 0.3))
            : isDue
                ? Border.all(color: Colors.orange.withValues(alpha: 0.3))
                : null,
      ),
      child: CupertinoListTile(
        padding: EdgeInsets.all(12),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: reminder.reminderType.icon == '🗓️'
                ? Colors.blue
                : reminder.reminderType.icon == '⚠️'
                    ? Colors.orange
                    : Colors.purple,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              reminder.reminderType.icon,
              style: TextStyle(fontSize: 20),
            ),
          ),
        ),
        title: Text(
          reminder.fileName,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            decoration:
                reminder.isCompleted ? TextDecoration.lineThrough : null,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4),
            Text(
              reminder.defaultMessage,
              style: TextStyle(
                fontSize: 12,
                color: CupertinoColors.systemGrey,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Text(
                  reminder.readableSize,
                  style: TextStyle(
                    fontSize: 11,
                    color: CupertinoColors.systemGrey,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  '•',
                  style: TextStyle(
                    fontSize: 11,
                    color: CupertinoColors.systemGrey,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  _formatReminderDate(reminder.reminderDate),
                  style: TextStyle(
                    fontSize: 11,
                    color: isOverdue
                        ? Colors.red
                        : isDue
                            ? Colors.orange
                            : CupertinoColors.systemGrey,
                    fontWeight: (isOverdue || isDue)
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
                if (isOverdue) ...[
                  SizedBox(width: 4),
                  Icon(
                    CupertinoIcons.exclamationmark_circle,
                    size: 12,
                    color: Colors.red,
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          child: Icon(
            CupertinoIcons.ellipsis,
            color: CupertinoColors.systemGrey,
          ),
          onPressed: () => _showReminderOptions(context, reminder),
        ),
      ),
    );
  }

  String _formatReminderDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);

    if (difference.isNegative) {
      final pastDifference = now.difference(date);
      if (pastDifference.inDays > 0) {
        return '${pastDifference.inDays} day${pastDifference.inDays > 1 ? 's' : ''} overdue';
      } else if (pastDifference.inHours > 0) {
        return '${pastDifference.inHours} hour${pastDifference.inHours > 1 ? 's' : ''} overdue';
      } else {
        return 'Overdue';
      }
    } else {
      if (difference.inDays > 0) {
        return 'In ${difference.inDays} day${difference.inDays > 1 ? 's' : ''}';
      } else if (difference.inHours > 0) {
        return 'In ${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}';
      } else {
        return 'Due now';
      }
    }
  }

  void _createNewReminder(BuildContext context) {
    context.toPage(CreateReminderPage());
  }

  void _showReminderOptions(BuildContext context, FileReminderModel reminder) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(reminder.fileName),
        message: Text(reminder.defaultMessage),
        actions: [
          if (!reminder.isCompleted)
            CupertinoActionSheetAction(
              child: Text('Mark as Complete'),
              onPressed: () {
                Navigator.pop(context);
                _markReminderComplete(reminder);
              },
            ),
          if (reminder.isCompleted)
            CupertinoActionSheetAction(
              child: Text('Mark as Incomplete'),
              onPressed: () {
                Navigator.pop(context);
                _markReminderIncomplete(reminder);
              },
            ),
          CupertinoActionSheetAction(
            child: Text('Edit Reminder'),
            onPressed: () {
              Navigator.pop(context);
              _editReminder(context, reminder);
            },
          ),
          if (reminder.isActive)
            CupertinoActionSheetAction(
              child: Text('Disable Reminder'),
              onPressed: () {
                Navigator.pop(context);
                _disableReminder(reminder);
              },
            ),
          if (!reminder.isActive)
            CupertinoActionSheetAction(
              child: Text('Enable Reminder'),
              onPressed: () {
                Navigator.pop(context);
                _enableReminder(reminder);
              },
            ),
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            child: Text('Delete Reminder'),
            onPressed: () {
              Navigator.pop(context);
              _deleteReminder(context, reminder);
            },
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: Text('Cancel'),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _markReminderComplete(FileReminderModel reminder) async {
    final dto = UpdateFileReminderDto(isCompleted: true);
    final success = await controller.updateFileReminder(reminder.id, dto);

    if (success) {
      VAppAlert.showSuccessSnackBar(
        context: context,
        message: 'Reminder marked as complete',
      );
    }
  }

  void _markReminderIncomplete(FileReminderModel reminder) async {
    final dto = UpdateFileReminderDto(isCompleted: false);
    final success = await controller.updateFileReminder(reminder.id, dto);

    if (success) {
      VAppAlert.showSuccessSnackBar(
        context: context,
        message: 'Reminder marked as incomplete',
      );
    }
  }

  void _disableReminder(FileReminderModel reminder) async {
    final dto = UpdateFileReminderDto(isActive: false);
    final success = await controller.updateFileReminder(reminder.id, dto);

    if (success) {
      VAppAlert.showSuccessSnackBar(
        context: context,
        message: 'Reminder disabled',
      );
    }
  }

  void _enableReminder(FileReminderModel reminder) async {
    final dto = UpdateFileReminderDto(isActive: true);
    final success = await controller.updateFileReminder(reminder.id, dto);

    if (success) {
      VAppAlert.showSuccessSnackBar(
        context: context,
        message: 'Reminder enabled',
      );
    }
  }

  void _editReminder(BuildContext context, FileReminderModel reminder) {
    // TODO: Navigate to edit reminder page
    VAppAlert.showSuccessSnackBar(
      context: context,
      message: 'Edit reminder coming soon',
    );
  }

  void _deleteReminder(BuildContext context, FileReminderModel reminder) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Delete Reminder'),
        content: Text(
            'Are you sure you want to delete this reminder? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: Text('Delete'),
            onPressed: () async {
              Navigator.pop(context);
              final success = await controller.deleteFileReminder(reminder.id);

              if (success) {
                VAppAlert.showSuccessSnackBar(
                  context: context,
                  message: 'Reminder deleted successfully',
                );
              }
            },
          ),
        ],
      ),
    );
  }
}
