// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up/app/core/models/storage/storage_dto.dart';
import 'package:super_up/app/modules/storage/controllers/storage_controller.dart';
import 'package:super_up_core/super_up_core.dart';

class CreateReminderPage extends StatefulWidget {
  const CreateReminderPage({super.key});

  @override
  State<CreateReminderPage> createState() => _CreateReminderPageState();
}

class _CreateReminderPageState extends State<CreateReminderPage> {
  late StorageController controller;
  final _fileIdController = TextEditingController();
  final _fileNameController = TextEditingController();
  final _fileSizeController = TextEditingController();
  final _customMessageController = TextEditingController();

  String selectedReminderType = 'oldFile';
  DateTime selectedDate = DateTime.now().add(const Duration(days: 30));
  DateTime selectedFileCreatedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    controller = StorageController();
  }

  @override
  void dispose() {
    _fileIdController.dispose();
    _fileNameController.dispose();
    _fileSizeController.dispose();
    _customMessageController.dispose();
    controller.onClose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text('Create Reminder'),
            leading: CupertinoButton(
              padding: EdgeInsets.zero,
              child: Text('Cancel'),
              onPressed: () => Navigator.pop(context),
            ),
            trailing: CupertinoButton(
              padding: EdgeInsets.zero,
              child: Text('Save'),
              onPressed: _saveReminder,
            ),
          )
        ],
        body: SafeArea(
          top: false,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // File Information Section
                _buildSectionHeader('File Information'),
                SizedBox(height: 12),

                CupertinoTextField(
                  controller: _fileIdController,
                  placeholder: 'File ID',
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.all(12),
                ),
                SizedBox(height: 12),

                CupertinoTextField(
                  controller: _fileNameController,
                  placeholder: 'File Name',
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.all(12),
                ),
                SizedBox(height: 12),

                CupertinoTextField(
                  controller: _fileSizeController,
                  placeholder: 'File Size (bytes)',
                  keyboardType: TextInputType.number,
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.all(12),
                ),
                SizedBox(height: 12),

                // File Created Date
                Container(
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CupertinoListTile(
                    title: Text('File Created Date'),
                    subtitle: Text(_formatDate(selectedFileCreatedDate)),
                    trailing: Icon(CupertinoIcons.chevron_right),
                    onTap: () => _showDatePicker(
                      context,
                      selectedFileCreatedDate,
                      (date) => setState(() => selectedFileCreatedDate = date),
                    ),
                  ),
                ),

                SizedBox(height: 24),

                // Reminder Settings Section
                _buildSectionHeader('Reminder Settings'),
                SizedBox(height: 12),

                // Reminder Type
                Container(
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      _buildReminderTypeOption('oldFile', '🗓️', 'Old File',
                          'Remind about old files'),
                      Divider(height: 1),
                      _buildReminderTypeOption('storageLimit', '⚠️',
                          'Storage Limit', 'Remind when storage is full'),
                      Divider(height: 1),
                      _buildReminderTypeOption(
                          'custom', '📝', 'Custom', 'Custom reminder message'),
                    ],
                  ),
                ),

                SizedBox(height: 16),

                // Reminder Date
                Container(
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CupertinoListTile(
                    title: Text('Reminder Date'),
                    subtitle: Text(_formatDate(selectedDate)),
                    trailing: Icon(CupertinoIcons.chevron_right),
                    onTap: () => _showDatePicker(
                      context,
                      selectedDate,
                      (date) => setState(() => selectedDate = date),
                    ),
                  ),
                ),

                SizedBox(height: 16),

                // Custom Message (only for custom type)
                if (selectedReminderType == 'custom') ...[
                  CupertinoTextField(
                    controller: _customMessageController,
                    placeholder: 'Custom reminder message',
                    maxLines: 3,
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemGrey6,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: EdgeInsets.all(12),
                  ),
                  SizedBox(height: 16),
                ],

                // Preview Section
                _buildSectionHeader('Preview'),
                SizedBox(height: 12),

                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemBlue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                        color:
                            CupertinoColors.systemBlue.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            _getReminderTypeIcon(selectedReminderType),
                            style: TextStyle(fontSize: 20),
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _fileNameController.text.isEmpty
                                  ? 'File Name'
                                  : _fileNameController.text,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: CupertinoColors.systemBlue,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        _getPreviewMessage(),
                        style: TextStyle(
                          fontSize: 14,
                          color: CupertinoColors.systemBlue,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Due: ${_formatDate(selectedDate)}',
                        style: TextStyle(
                          fontSize: 12,
                          color:
                              CupertinoColors.systemBlue.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildReminderTypeOption(
      String type, String icon, String title, String subtitle) {
    final isSelected = selectedReminderType == type;

    return CupertinoListTile(
      leading: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: isSelected
              ? CupertinoColors.systemBlue
              : CupertinoColors.systemGrey4,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: Text(icon, style: TextStyle(fontSize: 16)),
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: isSelected
          ? Icon(CupertinoIcons.checkmark, color: CupertinoColors.systemBlue)
          : null,
      onTap: () => setState(() => selectedReminderType = type),
    );
  }

  String _getReminderTypeIcon(String type) {
    switch (type) {
      case 'oldFile':
        return '🗓️';
      case 'storageLimit':
        return '⚠️';
      case 'custom':
        return '📝';
      default:
        return '🔔';
    }
  }

  String _getPreviewMessage() {
    if (selectedReminderType == 'custom' &&
        _customMessageController.text.isNotEmpty) {
      return _customMessageController.text;
    }

    switch (selectedReminderType) {
      case 'oldFile':
        return 'This file is old. Consider deleting it to free up space.';
      case 'storageLimit':
        return 'Your storage is almost full. Consider deleting this file.';
      case 'custom':
        return 'Custom reminder message';
      default:
        return 'File reminder';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDatePicker(BuildContext context, DateTime initialDate,
      Function(DateTime) onDateChanged) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => Container(
        height: 300,
        color: CupertinoColors.systemBackground,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    child: Text('Cancel'),
                    onPressed: () => Navigator.pop(context),
                  ),
                  CupertinoButton(
                    child: Text('Done'),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: initialDate,
                onDateTimeChanged: onDateChanged,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveReminder() async {
    // Validate inputs
    if (_fileIdController.text.isEmpty ||
        _fileNameController.text.isEmpty ||
        _fileSizeController.text.isEmpty) {
      VAppAlert.showErrorSnackBar(
        context: context,
        message: 'Please fill in all required fields',
      );
      return;
    }

    final fileSize = int.tryParse(_fileSizeController.text);
    if (fileSize == null) {
      VAppAlert.showErrorSnackBar(
        context: context,
        message: 'Please enter a valid file size',
      );
      return;
    }

    // Create reminder DTO
    final dto = CreateFileReminderDto(
      fileId: _fileIdController.text,
      fileName: _fileNameController.text,
      fileSize: fileSize,
      fileCreatedAt: selectedFileCreatedDate,
      reminderType: selectedReminderType,
      reminderDate: selectedDate,
      customMessage: selectedReminderType == 'custom'
          ? _customMessageController.text
          : null,
    );

    // Save reminder
    final success = await controller.createFileReminder(dto);

    if (success) {
      VAppAlert.showSuccessSnackBar(
        context: context,
        message: 'Reminder created successfully',
      );
      Navigator.pop(context);
    } else {
      VAppAlert.showErrorSnackBar(
        context: context,
        message: 'Failed to create reminder',
      );
    }
  }
}
