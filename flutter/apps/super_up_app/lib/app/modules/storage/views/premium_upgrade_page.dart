// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up/app/modules/storage/controllers/storage_controller.dart';

class PremiumUpgradePage extends StatefulWidget {
  const PremiumUpgradePage({super.key});

  @override
  State<PremiumUpgradePage> createState() => _PremiumUpgradePageState();
}

class _PremiumUpgradePageState extends State<PremiumUpgradePage> {
  late StorageController controller;
  String selectedPlan = 'monthly';

  @override
  void initState() {
    super.initState();
    controller = StorageController();
    controller.onInit();
    _loadStorageData();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  void _loadStorageData() async {
    await controller.loadStorageQuota();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text('Premium Storage'),
          )
        ],
        body: SafeArea(
          top: false,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Storage Status
                if (controller.hasStorageQuota) _buildCurrentStorageStatus(),

                SizedBox(height: 24),

                // Premium Benefits
                _buildPremiumBenefits(),

                SizedBox(height: 24),

                // Pricing Plans
                _buildPricingPlans(),

                SizedBox(height: 24),

                // Upgrade Button
                _buildUpgradeButton(),

                SizedBox(height: 16),

                // Terms and Conditions
                _buildTermsAndConditions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentStorageStatus() {
    final quota = controller.data.quota!;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Current Plan',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: quota.isPremium
                      ? Colors.purple
                      : CupertinoColors.systemBlue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  quota.isPremium ? 'PREMIUM' : 'FREE',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),

          // Storage usage bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              color: CupertinoColors.systemGrey4,
              borderRadius: BorderRadius.circular(4),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: quota.usagePercentage.clamp(0.0, 1.0),
              child: Container(
                decoration: BoxDecoration(
                  color: quota.isOverLimit
                      ? Colors.red
                      : quota.isNearLimit
                          ? Colors.orange
                          : Colors.green,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),

          SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${quota.usedReadable} used',
                style: TextStyle(
                  color: CupertinoColors.systemGrey,
                  fontSize: 14,
                ),
              ),
              Text(
                '${quota.totalQuotaReadable} total',
                style: TextStyle(
                  color: CupertinoColors.systemGrey,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumBenefits() {
    final benefits = [
      {
        'icon': '💾',
        'title': '10GB Storage',
        'description': '10x more space for your files'
      },
      {
        'icon': '📊',
        'title': 'Advanced Analytics',
        'description': 'Detailed insights into your storage usage'
      },
      {
        'icon': '🔔',
        'title': 'Smart Reminders',
        'description': 'Intelligent file cleanup suggestions'
      },
      {
        'icon': '☁️',
        'title': 'Auto-Backup',
        'description': 'Automatic backup of important files'
      },
      {
        'icon': '⚡',
        'title': 'Priority Support',
        'description': 'Get help faster with premium support'
      },
      {
        'icon': '🔒',
        'title': 'Enhanced Security',
        'description': 'Advanced encryption and security features'
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Premium Benefits',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16),
        ...benefits.map((benefit) => Container(
              margin: EdgeInsets.only(bottom: 12),
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple.withValues(alpha: 0.1)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        benefit['icon']!,
                        style: TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          benefit['title']!,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.purple,
                          ),
                        ),
                        Text(
                          benefit['description']!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.purple.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  Widget _buildPricingPlans() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Your Plan',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16),

        // Monthly Plan
        GestureDetector(
          onTap: () => setState(() => selectedPlan = 'monthly'),
          child: Container(
            padding: EdgeInsets.all(16),
            margin: EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: selectedPlan == 'monthly'
                  ? Colors.purple.withValues(alpha: 0.1)
                  : CupertinoColors.systemGrey6,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: selectedPlan == 'monthly'
                    ? Colors.purple
                    : Colors.transparent,
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: selectedPlan == 'monthly'
                          ? Colors.purple
                          : CupertinoColors.systemGrey,
                      width: 2,
                    ),
                    color: selectedPlan == 'monthly'
                        ? Colors.purple
                        : Colors.transparent,
                  ),
                  child: selectedPlan == 'monthly'
                      ? Icon(
                          CupertinoIcons.check_mark,
                          size: 12,
                          color: Colors.white,
                        )
                      : null,
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Monthly Plan',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'Billed monthly, cancel anytime',
                        style: TextStyle(
                          fontSize: 12,
                          color: CupertinoColors.systemGrey,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '\$4.99/month',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Yearly Plan
        GestureDetector(
          onTap: () => setState(() => selectedPlan = 'yearly'),
          child: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: selectedPlan == 'yearly'
                  ? Colors.purple.withValues(alpha: 0.1)
                  : CupertinoColors.systemGrey6,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: selectedPlan == 'yearly'
                    ? Colors.purple
                    : Colors.transparent,
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: selectedPlan == 'yearly'
                              ? Colors.purple
                              : CupertinoColors.systemGrey,
                          width: 2,
                        ),
                        color: selectedPlan == 'yearly'
                            ? Colors.purple
                            : Colors.transparent,
                      ),
                      child: selectedPlan == 'yearly'
                          ? Icon(
                              CupertinoIcons.check_mark,
                              size: 12,
                              color: Colors.white,
                            )
                          : null,
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Yearly Plan',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                              SizedBox(width: 8),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'SAVE 40%',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Text(
                            'Billed annually, best value',
                            style: TextStyle(
                              fontSize: 12,
                              color: CupertinoColors.systemGrey,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '\$29.99/year',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Colors.purple,
                          ),
                        ),
                        Text(
                          '\$2.50/month',
                          style: TextStyle(
                            fontSize: 12,
                            color: CupertinoColors.systemGrey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUpgradeButton() {
    return SizedBox(
      width: double.infinity,
      child: CupertinoButton.filled(
        onPressed: _handleComingSoon,
        child: Text(
          'Coming Soon - Premium Storage',
          style: TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildTermsAndConditions() {
    return Column(
      children: [
        Text(
          'By upgrading, you agree to our Terms of Service and Privacy Policy. '
          'Subscriptions auto-renew unless cancelled. You can manage your subscription '
          'in your account settings.',
          style: TextStyle(
            fontSize: 12,
            color: CupertinoColors.systemGrey,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            CupertinoButton(
              child: Text(
                'Terms of Service',
                style: TextStyle(fontSize: 12),
              ),
              onPressed: () => _showTermsOfService(),
            ),
            CupertinoButton(
              child: Text(
                'Privacy Policy',
                style: TextStyle(fontSize: 12),
              ),
              onPressed: () => _showPrivacyPolicy(),
            ),
          ],
        ),
      ],
    );
  }

  void _handleComingSoon() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('🚀 Coming Soon!'),
        content: Text(
          'Premium storage upgrade is coming soon! Stay tuned for exciting features including 10GB storage, advanced analytics, and priority support.',
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('OK'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Terms of Service'),
        content: Text(
          'This is a demo app. In a real implementation, this would show the actual terms of service.',
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('OK'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Privacy Policy'),
        content: Text(
          'This is a demo app. In a real implementation, this would show the actual privacy policy.',
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('OK'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }
}
