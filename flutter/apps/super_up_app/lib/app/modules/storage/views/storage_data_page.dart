// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up/app/modules/home/<USER>/settings_tab/widgets/settings_list_item_tile.dart';
import 'package:super_up/app/modules/storage/controllers/storage_controller.dart';
import 'package:super_up/app/modules/storage/views/storage_usage_page.dart';
import 'package:super_up/app/modules/storage/views/file_reminders_page.dart';
import 'package:super_up/app/modules/storage/views/premium_upgrade_page.dart';
import 'package:super_up_core/super_up_core.dart';

class StorageDataPage extends StatefulWidget {
  const StorageDataPage({super.key});

  @override
  State<StorageDataPage> createState() => _StorageDataPageState();
}

class _StorageDataPageState extends State<StorageDataPage> {
  late StorageController controller;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    controller = StorageController();
    controller.onInit();
    _loadData();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  void _loadData() async {
    setState(() => isLoading = true);
    await controller.loadStorageData();
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text('Storage & Data'),
          )
        ],
        body: SafeArea(
          top: false,
          child: isLoading
              ? Center(child: CupertinoActivityIndicator())
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // Storage Overview Section
                      _buildStorageOverview(context, controller.data),

                      // Storage Management Section
                      CupertinoListSection(
                        header: Text('Storage Management'),
                        dividerMargin: 0,
                        topMargin: 20,
                        hasLeading: false,
                        children: [
                          SettingsListItemTile(
                            color: Colors.blue,
                            title: 'Storage Usage',
                            subtitle: Text('View detailed storage breakdown'),
                            icon: CupertinoIcons.chart_pie,
                            onTap: () => context.toPage(StorageUsagePage()),
                            additionalInfo: Text(controller.usedStorageText),
                          ),
                          SettingsListItemTile(
                            color: Colors.orange,
                            title: 'File Reminders',
                            subtitle: Text('Manage file cleanup reminders'),
                            icon: CupertinoIcons.bell,
                            onTap: () => context.toPage(FileRemindersPage()),
                            additionalInfo: controller.dueRemindersCount > 0
                                ? Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      '${controller.dueRemindersCount}',
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 12),
                                    ),
                                  )
                                : null,
                          ),
                          if (!controller.isPremium)
                            SettingsListItemTile(
                              color: Colors.purple,
                              title: 'Upgrade to Premium',
                              subtitle: Text(
                                  'Get 10GB storage and advanced features'),
                              icon: CupertinoIcons.star_fill,
                              onTap: () => context.toPage(PremiumUpgradePage()),
                            ),
                        ],
                      ),

                      // Quick Actions Section
                      CupertinoListSection(
                        header: Text('Quick Actions'),
                        dividerMargin: 0,
                        topMargin: 20,
                        hasLeading: false,
                        children: [
                          SettingsListItemTile(
                            color: Colors.green,
                            title: 'Auto-cleanup Old Files',
                            subtitle: Text(
                                'Automatically delete files older than 1 year'),
                            icon: CupertinoIcons.trash,
                            onTap: () => _showAutoCleanupDialog(context),
                          ),
                          SettingsListItemTile(
                            color: Colors.red,
                            title: 'Clear Cache',
                            subtitle: Text(
                                'Free up space by clearing temporary files'),
                            icon: CupertinoIcons.clear,
                            onTap: () => _showClearCacheDialog(context),
                          ),
                        ],
                      ),

                      // Storage Tips Section
                      _buildStorageTips(context),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildStorageOverview(BuildContext context, StorageState state) {
    if (!controller.hasStorageQuota) {
      return Container(
        margin: EdgeInsets.all(16),
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: CupertinoColors.systemGrey6,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            CupertinoActivityIndicator(),
            SizedBox(width: 12),
            Text('Loading storage information...'),
          ],
        ),
      );
    }

    final usagePercentage = controller.storageUsagePercentage;

    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Storage Usage',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (controller.isPremium)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.purple,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'PREMIUM',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 12),

          // Progress bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              color: CupertinoColors.systemGrey4,
              borderRadius: BorderRadius.circular(4),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: usagePercentage.clamp(0.0, 1.0),
              child: Container(
                decoration: BoxDecoration(
                  color: controller.isOverStorageLimit
                      ? Colors.red
                      : controller.isNearStorageLimit
                          ? Colors.orange
                          : Colors.green,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),

          SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${controller.usedStorageText} used',
                style: TextStyle(
                  color: CupertinoColors.systemGrey,
                  fontSize: 14,
                ),
              ),
              Text(
                '${controller.totalStorageText} total',
                style: TextStyle(
                  color: CupertinoColors.systemGrey,
                  fontSize: 14,
                ),
              ),
            ],
          ),

          if (controller.isNearStorageLimit || controller.isOverStorageLimit)
            Container(
              margin: EdgeInsets.only(top: 12),
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: controller.isOverStorageLimit
                    ? Colors.red.withValues(alpha: 0.1)
                    : Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    CupertinoIcons.exclamationmark_triangle,
                    color: controller.isOverStorageLimit
                        ? Colors.red
                        : Colors.orange,
                    size: 16,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      controller.isOverStorageLimit
                          ? 'Storage limit exceeded. Please delete some files or upgrade to premium.'
                          : 'Storage is almost full. Consider cleaning up old files.',
                      style: TextStyle(
                        color: controller.isOverStorageLimit
                            ? Colors.red
                            : Colors.orange,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStorageTips(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(CupertinoIcons.lightbulb, color: CupertinoColors.systemBlue),
              SizedBox(width: 8),
              Text(
                'Storage Tips',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: CupertinoColors.systemBlue,
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Text(
            '• Set up file reminders to automatically clean old files\n'
            '• Compress videos before sharing to save space\n'
            '• Regularly review and delete unnecessary files\n'
            '• Upgrade to Premium for 10x more storage',
            style: TextStyle(
              fontSize: 14,
              color: CupertinoColors.systemBlue,
            ),
          ),
        ],
      ),
    );
  }

  void _showAutoCleanupDialog(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Auto-cleanup Old Files'),
        content: Text(
            'This will delete files older than 1 year. This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: Text('Delete'),
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement auto-cleanup
              VAppAlert.showSuccessSnackBar(
                context: context,
                message: 'Auto-cleanup completed',
              );
            },
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Clear Cache'),
        content: Text('This will clear temporary files and free up space.'),
        actions: [
          CupertinoDialogAction(
            child: Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          CupertinoDialogAction(
            child: Text('Clear'),
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement cache clearing
              VAppAlert.showSuccessSnackBar(
                context: context,
                message: 'Cache cleared successfully',
              );
            },
          ),
        ],
      ),
    );
  }
}
