// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up/app/core/models/storage/storage_metadata_model.dart';
import 'package:super_up/app/modules/storage/controllers/storage_controller.dart';
import 'package:super_up_core/super_up_core.dart';

class StorageUsagePage extends StatefulWidget {
  const StorageUsagePage({super.key});

  @override
  State<StorageUsagePage> createState() => _StorageUsagePageState();
}

class _StorageUsagePageState extends State<StorageUsagePage> {
  late StorageController controller;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    controller = StorageController();
    controller.onInit();
    _loadData();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  void _loadData() async {
    setState(() => isLoading = true);
    await controller.loadStorageData();
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text('Storage Usage'),
          )
        ],
        body: SafeArea(
          top: false,
          child: isLoading
              ? Center(child: CupertinoActivityIndicator())
              : Column(
                  children: [
                    // File type filter
                    _buildFileTypeFilter(context, controller.data),

                    // Storage breakdown
                    if (controller.hasStorageQuota)
                      _buildStorageBreakdown(context, controller.data),

                    // Files list
                    Expanded(
                      child: _buildFilesList(context, controller.data),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildFileTypeFilter(BuildContext context, StorageState state) {
    final fileTypes = [
      {'key': null, 'name': 'All Files', 'icon': '📁'},
      {'key': 'image', 'name': 'Images', 'icon': '📷'},
      {'key': 'video', 'name': 'Videos', 'icon': '🎥'},
      {'key': 'audio', 'name': 'Audio', 'icon': '🎵'},
      {'key': 'document', 'name': 'Documents', 'icon': '📄'},
    ];

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: fileTypes.length,
        itemBuilder: (context, index) {
          final type = fileTypes[index];
          final isSelected = state.selectedFileType == type['key'];

          return GestureDetector(
            onTap: () => controller.filterFilesByType(type['key']),
            child: Container(
              margin: EdgeInsets.only(right: 12),
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? CupertinoColors.systemBlue
                    : CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    type['icon'] as String,
                    style: TextStyle(fontSize: 24),
                  ),
                  SizedBox(height: 4),
                  Text(
                    type['name'] as String,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected ? Colors.white : CupertinoColors.label,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStorageBreakdown(BuildContext context, StorageState state) {
    final quota = state.quota!;
    final usageByFileType = quota.usageByFileType;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Storage Breakdown',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12),
          ...StorageFileType.values.map((type) {
            final usage = usageByFileType[type] ?? 0;
            final percentage =
                quota.usedBytes > 0 ? usage / quota.usedBytes : 0.0;

            return Padding(
              padding: EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Text(type.icon, style: TextStyle(fontSize: 16)),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(type.displayName),
                            Text(
                              _formatBytes(usage),
                              style: TextStyle(
                                color: CupertinoColors.systemGrey,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 4),
                        Container(
                          height: 4,
                          decoration: BoxDecoration(
                            color: CupertinoColors.systemGrey4,
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: percentage.clamp(0.0, 1.0),
                            child: Container(
                              decoration: BoxDecoration(
                                color: _getTypeColor(type),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildFilesList(BuildContext context, StorageState state) {
    if (state.isLoading && state.files.isEmpty) {
      return Center(child: CupertinoActivityIndicator());
    }

    if (state.files.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.folder,
              size: 64,
              color: CupertinoColors.systemGrey,
            ),
            SizedBox(height: 16),
            Text(
              'No files found',
              style: TextStyle(
                fontSize: 18,
                color: CupertinoColors.systemGrey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemCount: state.files.length + (state.hasMoreFiles ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.files.length) {
          // Load more indicator
          controller.loadFiles();
          return Container(
            padding: EdgeInsets.all(16),
            child: Center(child: CupertinoActivityIndicator()),
          );
        }

        final file = state.files[index];
        return _buildFileItem(context, file);
      },
    );
  }

  Widget _buildFileItem(BuildContext context, StorageMetadataModel file) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getTypeColor(file.storageFileType),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                file.storageFileType.icon,
                style: TextStyle(fontSize: 20),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.fileName,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      file.readableSize,
                      style: TextStyle(
                        color: CupertinoColors.systemGrey,
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      '•',
                      style: TextStyle(
                        color: CupertinoColors.systemGrey,
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      _formatDate(file.capturedAt),
                      style: TextStyle(
                        color: CupertinoColors.systemGrey,
                        fontSize: 12,
                      ),
                    ),
                    if (file.hasLocation) ...[
                      SizedBox(width: 8),
                      Icon(
                        CupertinoIcons.location,
                        size: 12,
                        color: CupertinoColors.systemGrey,
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          CupertinoButton(
            padding: EdgeInsets.zero,
            child: Icon(
              CupertinoIcons.ellipsis,
              color: CupertinoColors.systemGrey,
            ),
            onPressed: () => _showFileOptions(context, file),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(StorageFileType type) {
    switch (type) {
      case StorageFileType.image:
        return Colors.green;
      case StorageFileType.video:
        return Colors.blue;
      case StorageFileType.audio:
        return Colors.orange;
      case StorageFileType.document:
        return Colors.purple;
    }
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showFileOptions(BuildContext context, StorageMetadataModel file) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(file.fileName),
        actions: [
          CupertinoActionSheetAction(
            child: Text('Create Reminder'),
            onPressed: () {
              Navigator.pop(context);
              _createFileReminder(context, file);
            },
          ),
          CupertinoActionSheetAction(
            child: Text('View Details'),
            onPressed: () {
              Navigator.pop(context);
              _showFileDetails(context, file);
            },
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: Text('Cancel'),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _createFileReminder(BuildContext context, StorageMetadataModel file) {
    // TODO: Navigate to create reminder page
    VAppAlert.showSuccessSnackBar(
      context: context,
      message: 'Reminder creation coming soon',
    );
  }

  void _showFileDetails(BuildContext context, StorageMetadataModel file) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('File Details'),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Name: ${file.fileName}'),
            Text('Size: ${file.readableSize}'),
            Text('Type: ${file.storageFileType.displayName}'),
            Text('Created: ${_formatDate(file.capturedAt)}'),
            if (file.hasLocation)
              Text('Location: ${file.latitude}, ${file.longitude}'),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('OK'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }
}
