// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up/app/core/models/storage/storage_metadata_model.dart';
import 'package:super_up/app/modules/storage/controllers/storage_controller.dart';
import 'package:super_up_core/super_up_core.dart';

class StorageUsagePage extends StatefulWidget {
  const StorageUsagePage({super.key});

  @override
  State<StorageUsagePage> createState() => _StorageUsagePageState();
}

class _StorageUsagePageState extends State<StorageUsagePage> {
  late StorageController controller;

  @override
  void initState() {
    super.initState();
    controller = StorageController();
    controller.onInit();
    _loadData();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  void _loadData() async {
    await controller.loadStorageData();
    _checkStorageUsageAndNotify();
  }

  void _checkStorageUsageAndNotify() {
    if (!controller.hasStorageQuota || controller.isPremium) return;

    final quota = controller.data.quota!;
    final usagePercentage = quota.usagePercentage;

    // Show notification when user reaches 70% storage usage
    if (usagePercentage >= 0.70) {
      _showStorageWarningNotification(usagePercentage);
    }
  }

  void _showStorageWarningNotification(double usagePercentage) {
    final percentageText = '${(usagePercentage * 100).toInt()}%';

    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Row(
          children: [
            Icon(
              CupertinoIcons.exclamationmark_triangle_fill,
              color: CupertinoColors.systemOrange,
              size: 20,
            ),
            SizedBox(width: 8),
            Text('Storage Warning'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 8),
            Text(
              'You\'ve used $percentageText of your storage space.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 12),
            Text(
              'To continue using the app smoothly, consider:',
              style: TextStyle(
                fontSize: 14,
                color: CupertinoColors.systemGrey,
              ),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('Clear Space'),
            onPressed: () {
              Navigator.pop(context);
              _showClearSpaceOptions();
            },
          ),
          CupertinoDialogAction(
            child: Text(
              'Upgrade to Premium',
              style: TextStyle(
                color: CupertinoColors.systemBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
            onPressed: () {
              Navigator.pop(context);
              _showPremiumInfo();
            },
          ),
          CupertinoDialogAction(
            child: Text('Later'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  void _showClearSpaceOptions() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text('Clear Storage Space'),
        message: Text('Choose what to clean up'),
        actions: [
          CupertinoActionSheetAction(
            child: Text('Delete Old Images'),
            onPressed: () {
              Navigator.pop(context);
              VAppAlert.showSuccessSnackBar(
                context: context,
                message: 'Go to Images tab and delete old photos',
              );
            },
          ),
          CupertinoActionSheetAction(
            child: Text('Delete Old Videos'),
            onPressed: () {
              Navigator.pop(context);
              VAppAlert.showSuccessSnackBar(
                context: context,
                message: 'Go to Videos tab and delete old videos',
              );
            },
          ),
          CupertinoActionSheetAction(
            child: Text('Clear Cache'),
            onPressed: () {
              Navigator.pop(context);
              VAppAlert.showSuccessSnackBar(
                context: context,
                message: 'Cache cleared successfully',
              );
            },
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: Text('Cancel'),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _showPremiumInfo() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Row(
          children: [
            Icon(
              CupertinoIcons.star_fill,
              color: CupertinoColors.systemBlue,
              size: 20,
            ),
            SizedBox(width: 8),
            Text('Premium Storage'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 8),
            Text(
              'Upgrade to Premium for unlimited storage and never worry about space again!',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 12),
            Text(
              '• Unlimited storage space\n• Priority support\n• Advanced features',
              style: TextStyle(
                fontSize: 14,
                color: CupertinoColors.systemGrey,
              ),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('Learn More'),
            onPressed: () {
              Navigator.pop(context);
              VAppAlert.showSuccessSnackBar(
                context: context,
                message: 'Premium upgrade coming soon!',
              );
            },
          ),
          CupertinoDialogAction(
            child: Text('OK'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text('Storage Usage'),
          )
        ],
        body: SafeArea(
          top: false,
          child: ValueListenableBuilder(
            valueListenable: controller,
            builder: (context, state, child) {
              if (state.data.isLoading && state.data.files.isEmpty) {
                return Center(child: CupertinoActivityIndicator());
              }

              return Column(
                children: [
                  // File type filter
                  _buildFileTypeFilter(context, state.data),

                  // Storage breakdown
                  if (controller.hasStorageQuota)
                    _buildStorageBreakdown(context, state.data),

                  // Premium storage section
                  if (controller.hasStorageQuota && !controller.isPremium)
                    _buildPremiumUpgradeSection(context, state.data),

                  // Files list
                  Expanded(
                    child: _buildFilesList(context, state.data),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildFileTypeFilter(BuildContext context, StorageState state) {
    final fileTypes = [
      {'key': null, 'name': 'All Files', 'icon': '📁'},
      {'key': 'image', 'name': 'Images', 'icon': '📷'},
      {'key': 'video', 'name': 'Videos', 'icon': '🎥'},
      {'key': 'audio', 'name': 'Audio', 'icon': '🎵'},
      {'key': 'document', 'name': 'Documents', 'icon': '📄'},
    ];

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: fileTypes.length,
        itemBuilder: (context, index) {
          final type = fileTypes[index];
          final isSelected = state.selectedFileType == type['key'];

          return GestureDetector(
            onTap: () => controller.filterFilesByType(type['key']),
            child: Container(
              margin: EdgeInsets.only(right: 12),
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? CupertinoColors.systemBlue
                    : CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    type['icon'] as String,
                    style: TextStyle(fontSize: 24),
                  ),
                  SizedBox(height: 4),
                  Text(
                    type['name'] as String,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected ? Colors.white : CupertinoColors.label,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStorageBreakdown(BuildContext context, StorageState state) {
    final quota = state.quota!;
    final usageByFileType = quota.usageByFileType;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Storage Breakdown',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12),
          ...StorageFileType.values.map((type) {
            final usage = usageByFileType[type] ?? 0;
            final percentage =
                quota.usedBytes > 0 ? usage / quota.usedBytes : 0.0;

            return Padding(
              padding: EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Text(type.icon, style: TextStyle(fontSize: 16)),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(type.displayName),
                            Text(
                              _formatBytes(usage),
                              style: TextStyle(
                                color: CupertinoColors.systemGrey,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 4),
                        Container(
                          height: 4,
                          decoration: BoxDecoration(
                            color: CupertinoColors.systemGrey4,
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: percentage.clamp(0.0, 1.0),
                            child: Container(
                              decoration: BoxDecoration(
                                color: _getTypeColor(type),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPremiumUpgradeSection(BuildContext context, StorageState state) {
    final quota = state.quota!;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            CupertinoColors.systemBlue.withOpacity(0.1),
            CupertinoColors.systemPurple.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CupertinoColors.systemBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                CupertinoIcons.star_fill,
                color: CupertinoColors.systemBlue,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Upgrade to Premium',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: CupertinoColors.systemBlue,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'Get unlimited storage and premium features',
            style: TextStyle(
              fontSize: 14,
              color: CupertinoColors.systemGrey,
            ),
          ),
          SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current: ${quota.usedReadable} / ${quota.totalQuotaReadable}',
                    style: TextStyle(
                      fontSize: 12,
                      color: CupertinoColors.systemGrey,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Premium: Unlimited',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: CupertinoColors.systemBlue,
                    ),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: CupertinoColors.systemBlue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Premium',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilesList(BuildContext context, StorageState state) {
    if (state.isLoading && state.files.isEmpty) {
      return Center(child: CupertinoActivityIndicator());
    }

    if (state.files.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.folder,
              size: 64,
              color: CupertinoColors.systemGrey,
            ),
            SizedBox(height: 16),
            Text(
              'No files found',
              style: TextStyle(
                fontSize: 18,
                color: CupertinoColors.systemGrey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemCount: state.files.length + (state.hasMoreFiles ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.files.length) {
          // Load more indicator
          controller.loadFiles();
          return Container(
            padding: EdgeInsets.all(16),
            child: Center(child: CupertinoActivityIndicator()),
          );
        }

        final file = state.files[index];
        return _buildFileItem(context, file);
      },
    );
  }

  Widget _buildFileItem(BuildContext context, StorageMetadataModel file) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getTypeColor(file.storageFileType),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                file.storageFileType.icon,
                style: TextStyle(fontSize: 20),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.fileName,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      file.readableSize,
                      style: TextStyle(
                        color: CupertinoColors.systemGrey,
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      '•',
                      style: TextStyle(
                        color: CupertinoColors.systemGrey,
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      _formatDate(file.capturedAt),
                      style: TextStyle(
                        color: CupertinoColors.systemGrey,
                        fontSize: 12,
                      ),
                    ),
                    if (file.hasLocation) ...[
                      SizedBox(width: 8),
                      Icon(
                        CupertinoIcons.location,
                        size: 12,
                        color: CupertinoColors.systemGrey,
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          CupertinoButton(
            padding: EdgeInsets.zero,
            child: Icon(
              CupertinoIcons.ellipsis,
              color: CupertinoColors.systemGrey,
            ),
            onPressed: () => _showFileOptions(context, file),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(StorageFileType type) {
    switch (type) {
      case StorageFileType.image:
        return Colors.green;
      case StorageFileType.video:
        return Colors.blue;
      case StorageFileType.audio:
        return Colors.orange;
      case StorageFileType.document:
        return Colors.purple;
    }
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showFileOptions(BuildContext context, StorageMetadataModel file) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(file.fileName),
        actions: [
          CupertinoActionSheetAction(
            child: Text('View Details'),
            onPressed: () {
              Navigator.pop(context);
              _showFileDetails(context, file);
            },
          ),
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            child: Text('Delete File'),
            onPressed: () {
              Navigator.pop(context);
              _showDeleteConfirmation(context, file);
            },
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: Text('Cancel'),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(
      BuildContext context, StorageMetadataModel file) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Delete File'),
        content: Text(
          'Are you sure you want to permanently delete "${file.fileName}"? This action cannot be undone.',
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: Text('Delete'),
            onPressed: () async {
              Navigator.pop(context);
              await _deleteFile(context, file);
            },
          ),
        ],
      ),
    );
  }

  Future<void> _deleteFile(
      BuildContext context, StorageMetadataModel file) async {
    try {
      // Show loading indicator
      showCupertinoDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => CupertinoAlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CupertinoActivityIndicator(),
              SizedBox(height: 16),
              Text('Deleting file...'),
            ],
          ),
        ),
      );

      // Delete the file permanently
      final success = await controller.deleteFile(file);

      // Close loading dialog
      Navigator.pop(context);

      if (success) {
        // Show success message
        VAppAlert.showSuccessSnackBar(
          context: context,
          message: 'File deleted successfully',
        );
      } else {
        // Show error message
        VAppAlert.showErrorSnackBar(
          context: context,
          message: 'Failed to delete file',
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      Navigator.pop(context);

      // Show error message
      VAppAlert.showErrorSnackBar(
        context: context,
        message: 'Failed to delete file: ${e.toString()}',
      );
    }
  }

  void _showFileDetails(BuildContext context, StorageMetadataModel file) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('File Details'),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Name: ${file.fileName}'),
            Text('Size: ${file.readableSize}'),
            Text('Type: ${file.storageFileType.displayName}'),
            Text('Created: ${_formatDate(file.capturedAt)}'),
            if (file.hasLocation)
              Text('Location: ${file.latitude}, ${file.longitude}'),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('OK'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }
}
