// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up/app/core/api_service/storage/storage_api_service.dart';
import 'package:super_up/app/core/models/storage/storage_dto.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';

class MediaCaptureService {
  static MediaCaptureService? _instance;
  static MediaCaptureService get instance {
    _instance ??= MediaCaptureService._();
    return _instance!;
  }

  MediaCaptureService._();

  final _storageApiService = GetIt.I.get<StorageApiService>();

  /// Captures media with automatic metadata storage
  Future<VPlatformFile?> captureMediaWithMetadata(
      VPlatformFile mediaFile) async {
    try {
      // Get location if permission is granted
      Position? position;
      try {
        final locationPermission = await Geolocator.checkPermission();
        if (locationPermission == LocationPermission.always ||
            locationPermission == LocationPermission.whileInUse) {
          position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.medium,
            timeLimit: const Duration(seconds: 5),
          );
        }
      } catch (e) {
        // Location capture failed, continue without location
        if (kDebugMode) {
          print('Failed to get location: $e');
        }
      }

      // Create metadata
      final metadata = CreateStorageMetadataDto(
        fileId: mediaFile.fileHash,
        fileName: mediaFile.name,
        filePath: mediaFile.fileLocalPath ?? '',
        fileSize: await _getFileSize(mediaFile),
        fileType: _getFileType(mediaFile),
        mimeType: mediaFile.getMimeType ?? 'application/octet-stream',
        capturedAt: DateTime.now(),
        latitude: position?.latitude,
        longitude: position?.longitude,
        locationName: await _getLocationName(position),
        additionalMetadata: {
          'captureMethod': 'camera',
          'platform': VPlatforms.currentPlatform.toString(),
          'appVersion': '1.0.0',
        },
      );

      // Store metadata in background
      _storeMetadataInBackground(metadata);

      return mediaFile;
    } catch (e) {
      if (kDebugMode) {
        print('Error capturing media metadata: $e');
      }
      // Return the file even if metadata capture fails
      return mediaFile;
    }
  }

  /// Processes existing media file to add metadata
  Future<void> processExistingMedia(VPlatformFile mediaFile) async {
    try {
      final metadata = CreateStorageMetadataDto(
        fileId: mediaFile.fileHash,
        fileName: mediaFile.name,
        filePath: mediaFile.fileLocalPath ?? '',
        fileSize: await _getFileSize(mediaFile),
        fileType: _getFileType(mediaFile),
        mimeType: mediaFile.getMimeType ?? 'application/octet-stream',
        capturedAt: DateTime.now(),
        additionalMetadata: {
          'captureMethod': 'gallery',
          'platform': VPlatforms.currentPlatform.toString(),
          'appVersion': '1.0.0',
        },
      );

      _storeMetadataInBackground(metadata);
    } catch (e) {
      if (kDebugMode) {
        print('Error processing existing media metadata: $e');
      }
    }
  }

  /// Requests location permission if not already granted
  Future<bool> requestLocationPermission() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final result = await Geolocator.requestPermission();
        return result == LocationPermission.always ||
            result == LocationPermission.whileInUse;
      }
      return permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting location permission: $e');
      }
      return false;
    }
  }

  /// Checks if location services are enabled and permission is granted
  Future<bool> isLocationAvailable() async {
    try {
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return false;

      final permission = await Geolocator.checkPermission();
      return permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;
    } catch (e) {
      return false;
    }
  }

  Future<int> _getFileSize(VPlatformFile file) async {
    try {
      if (file.isFromBytes && file.bytes != null) {
        return file.bytes!.length;
      } else if (file.fileLocalPath != null) {
        final fileEntity = File(file.fileLocalPath!);
        if (await fileEntity.exists()) {
          return await fileEntity.length();
        }
      }
      return 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting file size: $e');
      }
      return 0;
    }
  }

  String _getFileType(VPlatformFile file) {
    final mimeType = file.getMimeType ?? '';
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    return 'document';
  }

  Future<String?> _getLocationName(Position? position) async {
    if (position == null) return null;

    try {
      // For now, just return coordinates as string
      // In a real app, you might use geocoding to get address
      return '${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}';
    } catch (e) {
      if (kDebugMode) {
        print('Error getting location name: $e');
      }
      return null;
    }
  }

  void _storeMetadataInBackground(CreateStorageMetadataDto metadata) {
    // Store metadata in background without blocking the UI
    Future.microtask(() async {
      try {
        await _storageApiService.createStorageMetadata(metadata);
        if (kDebugMode) {
          print('Media metadata stored successfully');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Failed to store media metadata: $e');
        }
      }
    });
  }

  /// Creates automatic file reminders for old files
  Future<void> createAutoReminder(
      String fileId, String fileName, int fileSize) async {
    try {
      // Create reminder for files older than 6 months
      final reminderDate = DateTime.now().add(const Duration(days: 180));

      final reminderDto = CreateFileReminderDto(
        fileId: fileId,
        fileName: fileName,
        fileSize: fileSize,
        fileCreatedAt: DateTime.now(),
        reminderType: 'oldFile',
        reminderDate: reminderDate,
      );

      await _storageApiService.createFileReminder(reminderDto);

      if (kDebugMode) {
        print('Auto reminder created for file: $fileName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to create auto reminder: $e');
      }
    }
  }

  /// Checks storage quota and shows warning if near limit
  Future<void> checkStorageQuota() async {
    try {
      final quota = await _storageApiService.getStorageQuota();

      if (quota.isNearLimit || quota.isOverLimit) {
        // Show storage warning
        VAppAlert.showSuccessSnackBarWithoutContext(
          message: quota.isOverLimit
              ? 'Storage limit exceeded! Please delete some files.'
              : 'Storage is almost full. Consider cleaning up old files.',
          duration: const Duration(seconds: 5),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to check storage quota: $e');
      }
    }
  }
}
