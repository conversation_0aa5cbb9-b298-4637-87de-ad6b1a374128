// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:io';

import 'package:chopper/chopper.dart';

import 'package:http/io_client.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';

import '../interceptors.dart';

part 'storage_api.chopper.dart';

@ChopperApi(baseUrl: 'storage')
abstract class StorageApi extends ChopperService {
  // Storage Metadata endpoints
  @POST(path: '/metadata')
  Future<Response> createStorageMetadata(@Body() Map<String, dynamic> body);

  @GET(path: '/metadata')
  Future<Response> getStorageMetadata(@QueryMap() Map<String, dynamic> query);

  @DELETE(path: '/metadata/{id}')
  Future<Response> deleteStorageMetadata(@Path('id') String id);

  @DELETE(path: '/file/{filePath}')
  Future<Response> deleteAllMetadataForFile(@Path('filePath') String filePath);

  // Storage Quota endpoints
  @GET(path: '/quota')
  Future<Response> getStorageQuota();

  @POST(path: '/upgrade-premium')
  Future<Response> upgradeToPremium(@Body() Map<String, dynamic> body);

  // File Reminder endpoints
  @POST(path: '/reminders')
  Future<Response> createFileReminder(@Body() Map<String, dynamic> body);

  @GET(path: '/reminders')
  Future<Response> getFileReminders(@QueryMap() Map<String, dynamic> query);

  @PUT(path: '/reminders/{id}')
  Future<Response> updateFileReminder(
    @Path('id') String id,
    @Body() Map<String, dynamic> body,
  );

  @DELETE(path: '/reminders/{id}')
  Future<Response> deleteFileReminder(@Path('id') String id);

  @GET(path: '/reminders/due')
  Future<Response> getDueReminders();

  // Analytics endpoints
  @GET(path: '/analytics')
  Future<Response> getStorageAnalytics();

  static StorageApi create({
    Uri? baseUrl,
    String? accessToken,
  }) {
    final client = ChopperClient(
      baseUrl: SConstants.sApiBaseUrl,
      services: [
        _$StorageApi(),
      ],
      converter: const JsonConverter(),
      interceptors: [AuthInterceptor()],
      errorConverter: ErrorInterceptor(),
      client: VPlatforms.isWeb
          ? null
          : IOClient(
              HttpClient()..connectionTimeout = const Duration(seconds: 10),
            ),
    );
    return _$StorageApi(client);
  }
}
