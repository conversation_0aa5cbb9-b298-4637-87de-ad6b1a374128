// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:io';

import 'package:chopper/chopper.dart';
import 'package:http/http.dart' hide Response, Request;
import 'package:http/io_client.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';

import '../interceptors.dart';

part 'storage_api.chopper.dart';

@ChopperApi(baseUrl: 'storage')
abstract class StorageApi extends ChopperService {
  // Storage Metadata endpoints
  @Post(path: '/metadata')
  Future<Response> createStorageMetadata(@Body() Map<String, dynamic> body);

  @Get(path: '/metadata')
  Future<Response> getStorageMetadata(@QueryMap() Map<String, dynamic> query);

  @Delete(path: '/metadata/{id}')
  Future<Response> deleteStorageMetadata(@Path('id') String id);

  // Storage Quota endpoints
  @Get(path: '/quota')
  Future<Response> getStorageQuota();

  @Post(path: '/upgrade-premium')
  Future<Response> upgradeToPremium(@Body() Map<String, dynamic> body);

  // File Reminder endpoints
  @Post(path: '/reminders')
  Future<Response> createFileReminder(@Body() Map<String, dynamic> body);

  @Get(path: '/reminders')
  Future<Response> getFileReminders(@QueryMap() Map<String, dynamic> query);

  @Put(path: '/reminders/{id}')
  Future<Response> updateFileReminder(
    @Path('id') String id,
    @Body() Map<String, dynamic> body,
  );

  @Delete(path: '/reminders/{id}')
  Future<Response> deleteFileReminder(@Path('id') String id);

  @Get(path: '/reminders/due')
  Future<Response> getDueReminders();

  // Analytics endpoints
  @Get(path: '/analytics')
  Future<Response> getStorageAnalytics();

  static StorageApi create({
    Uri? baseUrl,
    String? accessToken,
  }) {
    final client = ChopperClient(
      baseUrl: SConstants.sApiBaseUrl,
      services: [
        _$StorageApi(),
      ],
      converter: const JsonConverter(),
      interceptors: [AuthInterceptor()],
      errorConverter: ErrorInterceptor(),
      client: VPlatforms.isWeb
          ? null
          : IOClient(
              HttpClient()..connectionTimeout = const Duration(seconds: 10),
            ),
    );
    return _$StorageApi(client);
  }
}
