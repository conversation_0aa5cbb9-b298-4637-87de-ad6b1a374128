// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:super_up/app/core/api_service/storage/storage_api.dart';
import 'package:super_up/app/core/models/storage/storage_metadata_model.dart';
import 'package:super_up/app/core/models/storage/storage_quota_model.dart';
import 'package:super_up/app/core/models/storage/file_reminder_model.dart';
import 'package:super_up/app/core/models/storage/storage_dto.dart';
import 'package:super_up/app/core/api_service/interceptors.dart';

class StorageApiService {
  final StorageApi _api = StorageApi.create();

  // Storage Metadata methods
  Future<StorageMetadataModel> createStorageMetadata(
      CreateStorageMetadataDto dto) async {
    final response = await _api.createStorageMetadata(dto.toJson());
    final data = extractDataFromResponse(response);
    return StorageMetadataModel.fromJson(data);
  }

  Future<List<StorageMetadataModel>> getStorageMetadata({
    String? fileType,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    final query = StorageUsageQueryDto(
      fileType: fileType,
      startDate: startDate,
      endDate: endDate,
      page: page,
      limit: limit,
    );

    final response = await _api.getStorageMetadata(query.toQueryParams());
    final data = extractDataFromResponse(response);
    final List<dynamic> metadataList = data['data'];

    return metadataList
        .map((json) => StorageMetadataModel.fromJson(json))
        .toList();
  }

  Future<void> deleteStorageMetadata(String id) async {
    await _api.deleteStorageMetadata(id);
  }

  Future<void> deleteAllMetadataForFile(String filePath) async {
    // URL encode the file path to handle special characters
    final encodedFilePath = Uri.encodeComponent(filePath);
    await _api.deleteAllMetadataForFile(encodedFilePath);
  }

  // Storage Quota methods
  Future<StorageQuotaModel> getStorageQuota() async {
    final response = await _api.getStorageQuota();
    final data = extractDataFromResponse(response);
    return StorageQuotaModel.fromJson(data);
  }

  Future<StorageQuotaModel> upgradeToPremium(UpgradeToPremiumDto dto) async {
    final response = await _api.upgradeToPremium(dto.toJson());
    final data = extractDataFromResponse(response);
    return StorageQuotaModel.fromJson(data);
  }

  // File Reminder methods
  Future<FileReminderModel> createFileReminder(
      CreateFileReminderDto dto) async {
    final response = await _api.createFileReminder(dto.toJson());
    final data = extractDataFromResponse(response);
    return FileReminderModel.fromJson(data);
  }

  Future<List<FileReminderModel>> getFileReminders({
    bool? isActive,
    bool? isCompleted,
    String? reminderType,
    int page = 1,
    int limit = 20,
  }) async {
    final query = FileRemindersQueryDto(
      isActive: isActive,
      isCompleted: isCompleted,
      reminderType: reminderType,
      page: page,
      limit: limit,
    );

    final response = await _api.getFileReminders(query.toQueryParams());
    final data = extractDataFromResponse(response);
    final List<dynamic> remindersList = data['data'];

    return remindersList
        .map((json) => FileReminderModel.fromJson(json))
        .toList();
  }

  Future<FileReminderModel> updateFileReminder(
      String id, UpdateFileReminderDto dto) async {
    final response = await _api.updateFileReminder(id, dto.toJson());
    final data = extractDataFromResponse(response);
    return FileReminderModel.fromJson(data);
  }

  Future<void> deleteFileReminder(String id) async {
    await _api.deleteFileReminder(id);
  }

  Future<List<FileReminderModel>> getDueReminders() async {
    final response = await _api.getDueReminders();
    final data = extractDataFromResponse(response);

    // Handle both array and object responses
    final List<dynamic> remindersList =
        data is List ? data as List<dynamic> : [data];

    return remindersList
        .map((json) => FileReminderModel.fromJson(json))
        .toList();
  }

  // Analytics methods
  Future<Map<String, dynamic>> getStorageAnalytics() async {
    final response = await _api.getStorageAnalytics();
    return extractDataFromResponse(response);
  }
}
