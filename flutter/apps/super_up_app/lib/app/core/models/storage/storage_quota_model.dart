// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

class StorageQuotaModel {
  final String userId;
  final int totalQuotaBytes;
  final int usedBytes;
  final Map<String, int> usageByType;
  final bool isPremium;
  final DateTime? premiumExpiresAt;
  final DateTime lastCalculatedAt;

  StorageQuotaModel({
    required this.userId,
    required this.totalQuotaBytes,
    required this.usedBytes,
    required this.usageByType,
    required this.isPremium,
    this.premiumExpiresAt,
    required this.lastCalculatedAt,
  });

  factory StorageQuotaModel.fromJson(Map<String, dynamic> json) {
    return StorageQuotaModel(
      userId: json['userId'],
      totalQuotaBytes: json['totalQuotaBytes'],
      usedBytes: json['usedBytes'],
      usageByType: Map<String, int>.from(json['usageByType'] ?? {}),
      isPremium: json['isPremium'] ?? false,
      premiumExpiresAt: json['premiumExpiresAt'] != null 
          ? DateTime.parse(json['premiumExpiresAt']) 
          : null,
      lastCalculatedAt: DateTime.parse(json['lastCalculatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'totalQuotaBytes': totalQuotaBytes,
      'usedBytes': usedBytes,
      'usageByType': usageByType,
      'isPremium': isPremium,
      'premiumExpiresAt': premiumExpiresAt?.toIso8601String(),
      'lastCalculatedAt': lastCalculatedAt.toIso8601String(),
    };
  }

  // Getters for convenience
  int get availableBytes => totalQuotaBytes - usedBytes;
  double get usagePercentage => usedBytes / totalQuotaBytes;
  bool get isNearLimit => usagePercentage > 0.8;
  bool get isOverLimit => usedBytes > totalQuotaBytes;

  String get totalQuotaReadable => _formatBytes(totalQuotaBytes);
  String get usedReadable => _formatBytes(usedBytes);
  String get availableReadable => _formatBytes(availableBytes);

  // Default quotas
  static const int freeQuotaBytes = 1024 * 1024 * 1024; // 1GB
  static const int premiumQuotaBytes = 10 * 1024 * 1024 * 1024; // 10GB

  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  Map<StorageFileType, int> get usageByFileType {
    return {
      StorageFileType.image: usageByType['image'] ?? 0,
      StorageFileType.video: usageByType['video'] ?? 0,
      StorageFileType.audio: usageByType['audio'] ?? 0,
      StorageFileType.document: usageByType['document'] ?? 0,
    };
  }

  StorageQuotaModel copyWith({
    String? userId,
    int? totalQuotaBytes,
    int? usedBytes,
    Map<String, int>? usageByType,
    bool? isPremium,
    DateTime? premiumExpiresAt,
    DateTime? lastCalculatedAt,
  }) {
    return StorageQuotaModel(
      userId: userId ?? this.userId,
      totalQuotaBytes: totalQuotaBytes ?? this.totalQuotaBytes,
      usedBytes: usedBytes ?? this.usedBytes,
      usageByType: usageByType ?? this.usageByType,
      isPremium: isPremium ?? this.isPremium,
      premiumExpiresAt: premiumExpiresAt ?? this.premiumExpiresAt,
      lastCalculatedAt: lastCalculatedAt ?? this.lastCalculatedAt,
    );
  }
}

// Import the enum from the metadata model
enum StorageFileType {
  image,
  video,
  audio,
  document,
}
